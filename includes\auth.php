<?php
/**
 * Authentication Functions - Freight & Accounting Portal
 */

// Prevent direct access
if (!defined('PORTAL_ACCESS')) {
    define('PORTAL_ACCESS', true);
}

/**
 * Authenticate user via FTP credentials
 */
function authenticateFTP($username, $password) {
    // For demo purposes, we'll use hardcoded credentials
    // In production, this would connect to an actual FTP server
    $validCredentials = [
        '<EMAIL>' => 'admin123',
        '<EMAIL>' => 'admin123',
        'ftpuser' => 'ftppass',
        'testuser' => 'testpass'
    ];

    if (isset($validCredentials[$username]) && $validCredentials[$username] === $password) {
        // Create session for authenticated user
        $_SESSION['user_id'] = 1; // Demo user ID
        $_SESSION['username'] = $username;
        $_SESSION['last_activity'] = time();
        $_SESSION['is_admin'] = ($username === '<EMAIL>');
        $_SESSION['tenant_id'] = 1;

        // Log successful login
        error_log("Successful FTP login for username: " . $username . " from IP: " . $_SERVER['REMOTE_ADDR']);

        return true;
    }

    return false;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
        return false;
    }

    // Check session timeout
    if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
        session_destroy();
        return false;
    }

    // Update last activity
    $_SESSION['last_activity'] = time();

    return true;
}

/**
 * Get current user information
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT u.*, t.name as tenant_name 
            FROM users u 
            LEFT JOIN tenants t ON u.tenant_id = t.id 
            WHERE u.id = ? AND u.is_active = 1
        ");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Failed to get current user: " . $e->getMessage());
        return null;
    }
}

/**
 * Authenticate user
 */
function authenticateUser($username, $password) {
    global $db;
    
    try {
        // Check for too many failed attempts
        $stmt = $db->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE username = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$username, LOGIN_LOCKOUT_TIME]);
        $attempts = $stmt->fetch()['attempts'];
        
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            return [
                'success' => false,
                'message' => 'Account temporarily locked due to too many failed attempts. Please try again later.'
            ];
        }
        
        // Get user from database
        $stmt = $db->prepare("
            SELECT u.*, t.name as tenant_name 
            FROM users u 
            LEFT JOIN tenants t ON u.tenant_id = t.id 
            WHERE u.username = ? AND u.is_active = 1
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if (!$user || !verifyPassword($password, $user['password'])) {
            // Log failed attempt
            $stmt = $db->prepare("
                INSERT INTO login_attempts (username, ip_address, created_at) 
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$username, $_SERVER['REMOTE_ADDR']]);
            
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }
        
        // Clear failed attempts on successful login
        $stmt = $db->prepare("DELETE FROM login_attempts WHERE username = ?");
        $stmt->execute([$username]);
        
        // Update last login
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        return [
            'success' => true,
            'user' => $user
        ];
        
    } catch (PDOException $e) {
        error_log("Authentication error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Authentication system error. Please try again later.'
        ];
    }
}

/**
 * Get user permissions
 */
function getUserPermissions($userId) {
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT p.name 
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = ?
            UNION
            SELECT p.name 
            FROM permissions p
            INNER JOIN user_permissions up ON p.id = up.permission_id
            WHERE up.user_id = ?
        ");
        $stmt->execute([$userId, $userId]);
        
        $permissions = [];
        while ($row = $stmt->fetch()) {
            $permissions[] = $row['name'];
        }
        
        return $permissions;
    } catch (PDOException $e) {
        error_log("Failed to get user permissions: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1;
}

/**
 * Require login
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

/**
 * Require admin privileges
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('HTTP/1.0 403 Forbidden');
        die('Access denied. Admin privileges required.');
    }
}

/**
 * Check if user has specific permission
 */
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }

    // Admin users have all permissions
    if (isAdmin()) {
        return true;
    }

    $userPermissions = getUserPermissions($_SESSION['user_id']);
    return in_array($permission, $userPermissions);
}

/**
 * Require specific permission
 */
function requirePermission($permission) {
    requireLogin();

    if (!hasPermission($permission)) {
        header('HTTP/1.0 403 Forbidden');
        die('Access denied. Insufficient permissions.');
    }
}



/**
 * Get tenant ID for current user
 */
function getCurrentTenantId() {
    return $_SESSION['tenant_id'] ?? null;
}

/**
 * Check if user belongs to tenant
 */
function belongsToTenant($tenantId) {
    $currentTenantId = getCurrentTenantId();
    return $currentTenantId && ($currentTenantId == $tenantId || isAdmin());
}

/**
 * Create new user
 */
function createUser($userData) {
    global $db;
    
    try {
        // Validate required fields
        $required = ['username', 'email', 'password', 'tenant_id'];
        foreach ($required as $field) {
            if (empty($userData[$field])) {
                return ['success' => false, 'message' => "Field '$field' is required."];
            }
        }
        
        // Validate password
        if (!validatePassword($userData['password'])) {
            return ['success' => false, 'message' => 'Password does not meet requirements.'];
        }
        
        // Check if username exists
        $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$userData['username']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Username already exists.'];
        }
        
        // Check if email exists
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$userData['email']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Email already exists.'];
        }
        
        // Create user
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, tenant_id, is_admin, is_active, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW())
        ");
        
        $hashedPassword = hashPassword($userData['password']);
        $stmt->execute([
            $userData['username'],
            $userData['email'],
            $hashedPassword,
            $userData['first_name'] ?? '',
            $userData['last_name'] ?? '',
            $userData['tenant_id'],
            $userData['is_admin'] ?? 0
        ]);
        
        $userId = $db->lastInsertId();
        
        return ['success' => true, 'user_id' => $userId];
        
    } catch (PDOException $e) {
        error_log("Failed to create user: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to create user.'];
    }
}
