/**
 * Main JavaScript - Freight & Accounting Portal
 */

// Global application object
const FreightPortal = {
    // Configuration
    config: {
        sessionTimeout: 3600000, // 1 hour in milliseconds
        ajaxTimeout: 30000, // 30 seconds
        maxFileSize: ******** // 10MB
    },
    
    // Initialize application
    init: function() {
        this.setupEventListeners();
        this.initSessionTimeout();
        this.initTooltips();
        this.initModals();
        this.checkFlashMessages();
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions with loading states
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.classList.contains('ajax-form')) {
                e.preventDefault();
                FreightPortal.handleAjaxForm(form);
            } else {
                FreightPortal.showFormLoading(form);
            }
        });
        
        // Handle file uploads
        document.addEventListener('change', function(e) {
            if (e.target.type === 'file') {
                FreightPortal.validateFileUpload(e.target);
            }
        });
        
        // Handle modal triggers
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-modal')) {
                e.preventDefault();
                FreightPortal.openModal(e.target.getAttribute('data-modal'));
            }
            
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('modal-overlay')) {
                FreightPortal.closeModal();
            }
        });
        
        // Handle confirmation dialogs
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-confirm')) {
                if (!confirm(e.target.getAttribute('data-confirm'))) {
                    e.preventDefault();
                }
            }
        });
    },
    
    // Initialize session timeout warning
    initSessionTimeout: function() {
        let warningShown = false;
        
        setInterval(() => {
            const lastActivity = localStorage.getItem('lastActivity');
            const now = Date.now();
            
            if (lastActivity && (now - lastActivity) > (this.config.sessionTimeout - 300000)) { // 5 minutes before timeout
                if (!warningShown) {
                    this.showSessionWarning();
                    warningShown = true;
                }
            }
        }, 60000); // Check every minute
        
        // Update last activity on user interaction
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, () => {
                localStorage.setItem('lastActivity', Date.now());
                warningShown = false;
            }, { passive: true });
        });
    },
    
    // Show session timeout warning
    showSessionWarning: function() {
        this.showAlert('Your session will expire soon. Please save your work.', 'warning');
    },
    
    // Initialize tooltips
    initTooltips: function() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', function() {
                FreightPortal.showTooltip(this, this.getAttribute('data-tooltip'));
            });
            
            element.addEventListener('mouseleave', function() {
                FreightPortal.hideTooltip();
            });
        });
    },
    
    // Show tooltip
    showTooltip: function(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    },
    
    // Hide tooltip
    hideTooltip: function() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },
    
    // Initialize modals
    initModals: function() {
        // Close modal on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                FreightPortal.closeModal();
            }
        });
    },
    
    // Open modal
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Focus first input in modal
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }
    },
    
    // Close modal
    closeModal: function() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        document.body.style.overflow = '';
    },
    
    // Handle AJAX form submissions
    handleAjaxForm: function(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
        }
        
        fetch(form.action, {
            method: form.method || 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert(data.message || 'Operation completed successfully', 'success');
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
            } else {
                this.showAlert(data.message || 'An error occurred', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('An unexpected error occurred', 'error');
        })
        .finally(() => {
            // Remove loading state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
            }
        });
    },
    
    // Show form loading state
    showFormLoading: function(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
        }
    },
    
    // Validate file upload
    validateFileUpload: function(input) {
        const files = input.files;
        const errors = [];
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // Check file size
            if (file.size > this.config.maxFileSize) {
                errors.push(`File "${file.name}" is too large. Maximum size is ${this.formatFileSize(this.config.maxFileSize)}.`);
            }
            
            // Check file type (if specified)
            const allowedTypes = input.getAttribute('data-allowed-types');
            if (allowedTypes) {
                const types = allowedTypes.split(',');
                const fileExtension = file.name.split('.').pop().toLowerCase();
                if (!types.includes(fileExtension)) {
                    errors.push(`File "${file.name}" has an invalid type. Allowed types: ${types.join(', ')}.`);
                }
            }
        }
        
        if (errors.length > 0) {
            this.showAlert(errors.join('\n'), 'error');
            input.value = '';
        }
    },
    
    // Format file size
    formatFileSize: function(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
    },
    
    // Show alert message
    showAlert: function(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="alert-close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        // Insert at top of main content or body
        const target = document.querySelector('.main-content') || document.body;
        target.insertBefore(alert, target.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    },
    
    // Check for flash messages
    checkFlashMessages: function() {
        const flashMessage = document.querySelector('.flash-message');
        if (flashMessage) {
            const type = flashMessage.getAttribute('data-type') || 'info';
            const message = flashMessage.textContent;
            this.showAlert(message, type);
            flashMessage.remove();
        }
    },
    
    // Utility function to make AJAX requests
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            timeout: this.config.ajaxTimeout
        };
        
        const config = Object.assign(defaults, options);
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    FreightPortal.init();
});

// Export for use in other scripts
window.FreightPortal = FreightPortal;
