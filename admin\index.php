<?php
/**
 * Admin Dashboard - Freight & Accounting Portal
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/session.php';
require_once '../includes/auth.php';

// Require admin privileges
requireAdmin();

// Get current user
$currentUser = getCurrentUser();
$currentTenantId = getCurrentTenantId();

// Get dashboard statistics
$stats = getAdminStats();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Freight & Accounting Portal</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>Admin Dashboard</h1>
                <div class="user-info">
                    <span>Welcome, <?php echo htmlspecialchars($currentUser['username']); ?></span>
                    <a href="../index.php" class="btn btn-secondary">Back to Portal</a>
                    <a href="../logout.php" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <main class="main-content">
            <?php if ($flash = getFlashMessage()): ?>
                <div class="alert alert-<?php echo $flash['type']; ?>">
                    <?php echo htmlspecialchars($flash['text']); ?>
                </div>
            <?php endif; ?>

            <div class="admin-dashboard">
                <h2>System Administration</h2>
                
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                            <div class="stat-label">Total Users</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['total_tenants']; ?></div>
                            <div class="stat-label">Active Tenants</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📄</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['total_invoices']; ?></div>
                            <div class="stat-label">Total Invoices</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo formatCurrency($stats['total_revenue']); ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>
                </div>

                <!-- Admin Modules -->
                <div class="admin-modules">
                    <div class="module-card">
                        <a href="users.php">
                            <div class="module-icon">👤</div>
                            <h3>User Management</h3>
                            <p>Manage users, roles, and permissions</p>
                        </a>
                    </div>
                    
                    <div class="module-card">
                        <a href="tenants.php">
                            <div class="module-icon">🏢</div>
                            <h3>Tenant Management</h3>
                            <p>Manage client tenants and settings</p>
                        </a>
                    </div>
                    
                    <div class="module-card">
                        <a href="system-settings.php">
                            <div class="module-icon">⚙️</div>
                            <h3>System Settings</h3>
                            <p>Configure system parameters</p>
                        </a>
                    </div>
                    
                    <div class="module-card">
                        <a href="audit-logs.php">
                            <div class="module-icon">📋</div>
                            <h3>Audit Logs</h3>
                            <p>View system activity and security logs</p>
                        </a>
                    </div>
                    
                    <div class="module-card">
                        <a href="ftp-management.php">
                            <div class="module-icon">📁</div>
                            <h3>FTP Management</h3>
                            <p>Monitor FTP file processing</p>
                        </a>
                    </div>
                    
                    <div class="module-card">
                        <a href="payment-gateways.php">
                            <div class="module-icon">💳</div>
                            <h3>Payment Gateways</h3>
                            <p>Configure payment processors</p>
                        </a>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <h3>Recent System Activity</h3>
                    <div class="activity-list">
                        <?php foreach (getRecentActivity(10) as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <?php echo getActivityIcon($activity['action']); ?>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">
                                    <?php echo htmlspecialchars($activity['description']); ?>
                                </div>
                                <div class="activity-meta">
                                    <?php if ($activity['username']): ?>
                                        by <?php echo htmlspecialchars($activity['username']); ?>
                                    <?php endif; ?>
                                    • <?php echo formatDate($activity['created_at'], 'M j, Y g:i A'); ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>

<?php
/**
 * Get admin dashboard statistics
 */
function getAdminStats() {
    global $db;
    
    try {
        $stats = [];
        
        // Total users
        $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stats['total_users'] = $stmt->fetch()['count'];
        
        // Total tenants
        $stmt = $db->query("SELECT COUNT(*) as count FROM tenants WHERE is_active = 1");
        $stats['total_tenants'] = $stmt->fetch()['count'];
        
        // Total invoices
        $stmt = $db->query("SELECT COUNT(*) as count FROM invoices");
        $stats['total_invoices'] = $stmt->fetch()['count'];
        
        // Total revenue
        $stmt = $db->query("SELECT COALESCE(SUM(amount), 0) as total FROM invoices WHERE status = 'paid'");
        $stats['total_revenue'] = $stmt->fetch()['total'];
        
        return $stats;
    } catch (PDOException $e) {
        error_log("Failed to get admin stats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'total_tenants' => 0,
            'total_invoices' => 0,
            'total_revenue' => 0
        ];
    }
}

/**
 * Get recent activity
 */
function getRecentActivity($limit = 10) {
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT al.*, u.username 
            FROM activity_logs al 
            LEFT JOIN users u ON al.user_id = u.id 
            ORDER BY al.created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get recent activity: " . $e->getMessage());
        return [];
    }
}

/**
 * Get activity icon
 */
function getActivityIcon($action) {
    $icons = [
        'login' => '🔐',
        'logout' => '🚪',
        'user_created' => '👤',
        'user_updated' => '✏️',
        'user_deleted' => '🗑️',
        'invoice_created' => '📄',
        'payment_processed' => '💰',
        'security_event' => '⚠️',
        'system_setting' => '⚙️'
    ];
    
    return $icons[$action] ?? '📝';
}
?>
