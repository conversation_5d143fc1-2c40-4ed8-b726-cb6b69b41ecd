<?php
/**
 * User Management - Freight & Accounting Portal
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/session.php';
require_once '../includes/auth.php';

// Require admin privileges
requireAdmin();

$currentUser = getCurrentUser();
$currentTenantId = getCurrentTenantId();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_user':
                handleCreateUser();
                break;
            case 'update_user':
                handleUpdateUser();
                break;
            case 'delete_user':
                handleDeleteUser();
                break;
            case 'reset_password':
                handleResetPassword();
                break;
        }
    }
}

// Get users list
$users = getUsers();
$tenants = getTenants();
$roles = getRoles();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Portal</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>User Management</h1>
                <div class="user-info">
                    <a href="index.php" class="btn btn-secondary">Back to Admin</a>
                    <a href="../logout.php" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <main class="main-content">
            <?php if ($flash = getFlashMessage()): ?>
                <div class="alert alert-<?php echo $flash['type']; ?>">
                    <?php echo htmlspecialchars($flash['text']); ?>
                </div>
            <?php endif; ?>

            <div class="admin-section">
                <div class="section-header">
                    <h2>Users</h2>
                    <button class="btn btn-primary" data-modal="create-user-modal">Add New User</button>
                </div>

                <div class="users-table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Name</th>
                                <th>Tenant</th>
                                <th>Admin</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                                <td><?php echo htmlspecialchars($user['tenant_name'] ?? 'N/A'); ?></td>
                                <td>
                                    <span class="badge <?php echo $user['is_admin'] ? 'badge-success' : 'badge-secondary'; ?>">
                                        <?php echo $user['is_admin'] ? 'Yes' : 'No'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge <?php echo $user['is_active'] ? 'badge-success' : 'badge-danger'; ?>">
                                        <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td><?php echo $user['last_login'] ? formatDate($user['last_login'], 'M j, Y g:i A') : 'Never'; ?></td>
                                <td class="actions">
                                    <button class="btn btn-sm btn-primary" onclick="editUser(<?php echo $user['id']; ?>)">Edit</button>
                                    <button class="btn btn-sm btn-warning" onclick="resetPassword(<?php echo $user['id']; ?>)">Reset Password</button>
                                    <?php if ($user['id'] != $currentUser['id']): ?>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUser(<?php echo $user['id']; ?>)">Delete</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Create User Modal -->
    <div id="create-user-modal" class="modal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New User</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form method="POST" class="modal-body">
                <input type="hidden" name="action" value="create_user">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name">
                    </div>
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="tenant_id">Tenant</label>
                    <select id="tenant_id" name="tenant_id" required>
                        <option value="">Select Tenant</option>
                        <?php foreach ($tenants as $tenant): ?>
                        <option value="<?php echo $tenant['id']; ?>"><?php echo htmlspecialchars($tenant['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                    <small>Minimum 8 characters with uppercase, lowercase, number, and special character</small>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="is_admin" value="1">
                        Administrator privileges
                    </label>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-close">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function editUser(userId) {
            // Implementation for edit user modal
            alert('Edit user functionality will be implemented');
        }
        
        function resetPassword(userId) {
            if (confirm('Are you sure you want to reset this user\'s password?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>

<?php
/**
 * Handle user creation
 */
function handleCreateUser() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('users.php', 'Invalid request', 'error');
        return;
    }
    
    $userData = [
        'username' => sanitizeInput($_POST['username'] ?? ''),
        'email' => sanitizeInput($_POST['email'] ?? ''),
        'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
        'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
        'tenant_id' => (int)($_POST['tenant_id'] ?? 0),
        'password' => $_POST['password'] ?? '',
        'is_admin' => isset($_POST['is_admin']) ? 1 : 0
    ];
    
    $result = createUser($userData);
    
    if ($result['success']) {
        logActivity($_SESSION['user_id'], 'user_created', "Created user: {$userData['username']}");
        redirectWithMessage('users.php', 'User created successfully', 'success');
    } else {
        redirectWithMessage('users.php', $result['message'], 'error');
    }
}

/**
 * Handle password reset
 */
function handleResetPassword() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('users.php', 'Invalid request', 'error');
        return;
    }
    
    $userId = (int)($_POST['user_id'] ?? 0);
    $newPassword = generateSecurePassword();
    
    global $db;
    try {
        $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([hashPassword($newPassword), $userId]);
        
        logActivity($_SESSION['user_id'], 'password_reset', "Reset password for user ID: $userId");
        redirectWithMessage('users.php', "Password reset successfully. New password: $newPassword", 'success');
    } catch (PDOException $e) {
        error_log("Failed to reset password: " . $e->getMessage());
        redirectWithMessage('users.php', 'Failed to reset password', 'error');
    }
}

/**
 * Handle user deletion
 */
function handleDeleteUser() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('users.php', 'Invalid request', 'error');
        return;
    }
    
    $userId = (int)($_POST['user_id'] ?? 0);
    
    global $db;
    try {
        $stmt = $db->prepare("UPDATE users SET is_active = 0 WHERE id = ? AND id != ?");
        $stmt->execute([$userId, $_SESSION['user_id']]);
        
        logActivity($_SESSION['user_id'], 'user_deleted', "Deactivated user ID: $userId");
        redirectWithMessage('users.php', 'User deactivated successfully', 'success');
    } catch (PDOException $e) {
        error_log("Failed to delete user: " . $e->getMessage());
        redirectWithMessage('users.php', 'Failed to delete user', 'error');
    }
}

/**
 * Get all users
 */
function getUsers() {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT u.*, t.name as tenant_name 
            FROM users u 
            LEFT JOIN tenants t ON u.tenant_id = t.id 
            ORDER BY u.created_at DESC
        ");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get users: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all tenants
 */
function getTenants() {
    global $db;
    
    try {
        $stmt = $db->query("SELECT * FROM tenants WHERE is_active = 1 ORDER BY name");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get tenants: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all roles
 */
function getRoles() {
    global $db;
    
    try {
        $stmt = $db->query("SELECT * FROM roles ORDER BY name");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get roles: " . $e->getMessage());
        return [];
    }
}
?>
