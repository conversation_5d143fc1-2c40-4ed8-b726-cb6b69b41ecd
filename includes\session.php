<?php
/**
 * Session Management - Freight & Accounting Portal  
 *Session is a server-side storage of information that is used to persist user data across multiple pages (requests) during
 *  a user's visit to a website or web app.
 */

// Prevent direct access
if (!defined('PORTAL_ACCESS')) {
    define('PORTAL_ACCESS', true);
}

/**
 * Initialize secure session
 */
function initSecureSession() {
    // Session configuration for security
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // Set session name
    session_name('FREIGHT_PORTAL_SESSION');
    
    // Set session parameters
    session_set_cookie_params([
        'lifetime' => SESSION_TIMEOUT,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Regenerate session ID periodically for security
    if (!isset($_SESSION['created'])) {
        $_SESSION['created'] = time();
    } elseif (time() - $_SESSION['created'] > 1800) { // 30 minutes
        session_regenerate_id(true);
        $_SESSION['created'] = time();
    }
    
    // Check for session hijacking
    if (!validateSession()) {
        destroySession();
        return false;
    }
    
    return true;
}

/**
 * Validate session integrity
 */
function validateSession() {
    // Check if session variables are set
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
        return true; // Not logged in, validation not needed
    }
    
    // Check session timeout
    if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
        logActivity($_SESSION['user_id'], 'session_timeout', 'Session expired due to inactivity');
        return false;
    }
    
    // Check user agent consistency (basic hijacking protection)
    $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if (isset($_SESSION['user_agent'])) {
        if ($_SESSION['user_agent'] !== $currentUserAgent) {
            logSecurityEvent('Session Hijacking Attempt', 'User agent mismatch', 'CRITICAL');
            return false;
        }
    } else {
        $_SESSION['user_agent'] = $currentUserAgent;
    }
    
    // Check IP address consistency (optional, can be disabled for mobile users)
    $checkIP = getSystemSetting('session_check_ip', '1') === '1';
    if ($checkIP) {
        $currentIP = $_SERVER['REMOTE_ADDR'] ?? '';
        if (isset($_SESSION['ip_address'])) {
            if ($_SESSION['ip_address'] !== $currentIP) {
                logSecurityEvent('Session Hijacking Attempt', 'IP address mismatch', 'CRITICAL');
                return false;
            }
        } else {
            $_SESSION['ip_address'] = $currentIP;
        }
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    return true;
}

/**
 * Create user session
 */
function createUserSession($user) {
    // Regenerate session ID for security
    session_regenerate_id(true);
    
    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['tenant_id'] = $user['tenant_id'];
    $_SESSION['is_admin'] = $user['is_admin'];
    $_SESSION['last_activity'] = time();
    $_SESSION['created'] = time();
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // Set session token for additional security
    $_SESSION['session_token'] = generateToken(32);
    
    // Log successful login
    logActivity($user['id'], 'login', 'User logged in successfully');
    
    return true;
}

/**
 * Destroy session securely
 */
function destroySession() {
    // Log logout if user was logged in
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'logout', 'User session destroyed');
    }
    
    // Clear session data
    $_SESSION = [];
    
    // Delete session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy session
    session_destroy();
}

/**
 * Check if user is logged in
 */
function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && validateSession();
}

/**
 * Get session time remaining
 */
function getSessionTimeRemaining() {
    if (!isset($_SESSION['last_activity'])) {
        return 0;
    }
    
    $elapsed = time() - $_SESSION['last_activity'];
    $remaining = SESSION_TIMEOUT - $elapsed;
    
    return max(0, $remaining);
}

/**
 * Extend session
 */
function extendSession() {
    if (isUserLoggedIn()) {
        $_SESSION['last_activity'] = time();
        return true;
    }
    return false;
}

/**
 * Get session info
 */
function getSessionInfo() {
    if (!isUserLoggedIn()) {
        return null;
    }
    
    return [
        'user_id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'],
        'tenant_id' => $_SESSION['tenant_id'],
        'is_admin' => $_SESSION['is_admin'],
        'last_activity' => $_SESSION['last_activity'],
        'time_remaining' => getSessionTimeRemaining(),
        'session_token' => $_SESSION['session_token'] ?? null
    ];
}

/**
 * Clean up expired sessions (call this periodically)
 */
function cleanupExpiredSessions() {
    global $db;
    
    try {
        // This would require a sessions table to track active sessions
        // For now, we'll just clean up old login attempts and activity logs
        
        // Clean old login attempts (older than 24 hours)
        $stmt = $db->prepare("
            DELETE FROM login_attempts 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        
        // Clean old activity logs (based on retention setting)
        $retentionDays = getSystemSetting('audit_log_retention', '365');
        $stmt = $db->prepare("
            DELETE FROM activity_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$retentionDays]);
        
        return true;
    } catch (PDOException $e) {
        error_log("Failed to cleanup expired sessions: " . $e->getMessage());
        return false;
    }
}

/**
 * Force logout user (admin function)
 */
function forceLogoutUser($userId) {
    try {
        // Log the forced logout
        logActivity($userId, 'forced_logout', 'User was forcibly logged out by admin');

        // In a more advanced implementation, you would invalidate the user's session
        // For now, we'll just log the event

        return true;
    } catch (Exception $e) {
        error_log("Failed to force logout user: " . $e->getMessage());
        return false;
    }
}

/**
 * Check for concurrent sessions (if implemented)
 */
function checkConcurrentSessions($userId) {
    // This would require a sessions table to track active sessions
    // For now, we'll return true (allow concurrent sessions)
    // The $userId parameter is reserved for future implementation
    return true;
}

/**
 * Session security headers
 */
function setSecurityHeaders() {
    // Prevent clickjacking
    header('X-Frame-Options: DENY');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // XSS protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy (basic)
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;");
    
    // HTTPS enforcement (if using HTTPS)
    if (isset($_SERVER['HTTPS'])) {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

// Initialize session when this file is included
if (!headers_sent()) {
    setSecurityHeaders();
}

initSecureSession();
