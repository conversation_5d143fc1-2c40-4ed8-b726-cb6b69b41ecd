<?php
/**
 * Freight & Accounting Portal - Main Entry Point
 * Multi-tenant web application for freight and accounting system
 */

// Include configuration and core files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/session.php';
require_once 'includes/auth.php';
require_once 'includes/tenant.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Get user information
$user = getCurrentUser();
$userPermissions = getUserPermissions($user['id']);

// Get tenant dashboard data
$dashboardData = getTenantDashboardData();
$tenantStats = $dashboardData['statistics'] ?? [];
$recentInvoices = $dashboardData['recent_invoices'] ?? [];
$recentActivity = $dashboardData['recent_activity'] ?? [];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freight & Accounting Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>Freight & Accounting Portal</h1>
                <div class="user-info">
                    <span>Welcome, <?php echo htmlspecialchars($user['username']); ?></span>
                    <?php if ($user['tenant_name']): ?>
                        <span class="tenant-info">| <?php echo htmlspecialchars($user['tenant_name']); ?></span>
                    <?php endif; ?>
                    <?php if ($user['is_admin']): ?>
                        <a href="admin/index.php" class="btn btn-secondary">Admin Panel</a>
                    <?php endif; ?>
                    <a href="logout.php" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <main class="main-content">
            <?php if ($flash = getFlashMessage()): ?>
                <div class="alert alert-<?php echo $flash['type']; ?>">
                    <?php echo htmlspecialchars($flash['text']); ?>
                </div>
            <?php endif; ?>

            <div class="dashboard">
                <h2>Dashboard</h2>

                <!-- Tenant Statistics -->
                <?php if (!empty($tenantStats)): ?>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📄</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $tenantStats['total_invoices']; ?></div>
                            <div class="stat-label">Total Invoices</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $tenantStats['paid_invoices']; ?></div>
                            <div class="stat-label">Paid Invoices</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $tenantStats['pending_invoices']; ?></div>
                            <div class="stat-label">Pending Invoices</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo formatCurrency($tenantStats['total_revenue']); ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="module-grid">
                    <?php if (hasPermission($userPermissions, 'invoices')): ?>
                    <div class="module-card">
                        <a href="modules/invoices/index.php">
                            <div class="module-icon">📄</div>
                            <h3>Invoices</h3>
                            <p>Manage client invoices and payments</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (hasPermission($userPermissions, 'freight')): ?>
                    <div class="module-card">
                        <a href="modules/freight/index.php">
                            <div class="module-icon">🚛</div>
                            <h3>Freight System</h3>
                            <p>Freight management and tracking</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (hasPermission($userPermissions, 'tariff')): ?>
                    <div class="module-card">
                        <a href="modules/tariff/index.php">
                            <div class="module-icon">📊</div>
                            <h3>Tariff Book</h3>
                            <p>Manage tariffs and pricing</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (hasPermission($userPermissions, 'accounting')): ?>
                    <div class="module-card">
                        <a href="modules/accounting/index.php">
                            <div class="module-icon">💰</div>
                            <h3>Accounting</h3>
                            <p>Financial management and reports</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (hasPermission($userPermissions, 'backoffice')): ?>
                    <div class="module-card">
                        <a href="modules/backoffice/index.php">
                            <div class="module-icon">⚙️</div>
                            <h3>Back Office</h3>
                            <p>Administrative functions</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (hasPermission($userPermissions, 'admin')): ?>
                    <div class="module-card admin-module">
                        <a href="admin/index.php">
                            <div class="module-icon">👤</div>
                            <h3>User Management</h3>
                            <p>Manage users and permissions</p>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
