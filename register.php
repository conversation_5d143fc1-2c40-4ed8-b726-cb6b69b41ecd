<?php
/**
 * Registration Page - Freight & Accounting Portal
 */

// Include configuration and functions
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/session.php';
require_once 'includes/auth.php';

// Check for suspicious activity
if (detectSuspiciousActivity()) {
    logSecurityEvent('Suspicious Registration Attempt', 'Suspicious activity detected on registration page', 'WARNING');
}

// Check IP restrictions
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
if (isIPBlacklisted($clientIP)) {
    logSecurityEvent('Blocked IP Access', "Blocked IP attempted registration: $clientIP", 'WARNING');
    die('Access denied.');
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
        logSecurityEvent('CSRF Attack', 'Invalid CSRF token on registration', 'WARNING');
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $firstName = sanitizeInput($_POST['first_name'] ?? '');
        $lastName = sanitizeInput($_POST['last_name'] ?? '');

        // Validation
        if (empty($username) || empty($email) || empty($password) || empty($confirmPassword) || empty($firstName) || empty($lastName)) {
            $error = 'Please fill in all required fields.';
        } elseif (!validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } elseif (!validatePassword($password)) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long and contain uppercase, lowercase, and numeric characters.';
        } else {
            // Check if username or email already exists
            global $db;
            try {
                $stmt = $db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $email]);
                
                if ($stmt->fetch()) {
                    $error = 'Username or email already exists.';
                } else {
                    // Create new user
                    $userData = [
                        'username' => $username,
                        'email' => $email,
                        'password' => $password,
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'is_active' => 1,
                        'tenant_id' => 1 // Default tenant for now
                    ];
                    
                    $result = createUser($userData);
                    
                    if ($result['success']) {
                        $success = 'Account created successfully! You can now sign in.';
                        // Clear form data
                        $username = $email = $firstName = $lastName = '';
                    } else {
                        $error = $result['message'];
                    }
                }
            } catch (PDOException $e) {
                error_log("Registration error: " . $e->getMessage());
                $error = 'An error occurred during registration. Please try again.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Freight & Accounting Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/login.css">
</head>
<body class="login-page register-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo-container">
                    <img src="assets/img/logo.png" alt="ACS Logo" class="login-logo">
                </div>
                <h1>ACS Freight & Accounting Portal</h1>
                <p>Create your account</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="register.php" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" required
                               value="<?php echo htmlspecialchars($firstName ?? ''); ?>"
                               autocomplete="given-name">
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" required
                               value="<?php echo htmlspecialchars($lastName ?? ''); ?>"
                               autocomplete="family-name">
                    </div>
                </div>

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required
                           value="<?php echo htmlspecialchars($email ?? ''); ?>"
                           autocomplete="email">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required
                           autocomplete="new-password">
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required
                           autocomplete="new-password">
                </div>

                <button type="submit" class="login-btn">Create Account</button>
            </form>
            
            <div class="login-links">
                <p>Already have an account? <a href="login.php" class="register-link">Sign In</a></p>
            </div>
            
            <div class="login-footer">
                <p>&copy; <?php echo date('Y'); ?> ACS Freight & Accounting Portal. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script src="assets/js/login.js"></script>
</body>
</html>
