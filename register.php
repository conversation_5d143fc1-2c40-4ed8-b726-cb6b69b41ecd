<?php
/**
 * Registration Page - Freight & Accounting Portal
 */

// Include configuration and functions
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/session.php';
require_once 'includes/auth.php';

// Check for suspicious activity
if (detectSuspiciousActivity()) {
    logSecurityEvent('Suspicious Registration Attempt', 'Suspicious activity detected on registration page', 'WARNING');
}

// Check IP restrictions
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
if (isIPBlacklisted($clientIP)) {
    logSecurityEvent('Blocked IP Access', "Blocked IP attempted registration: $clientIP", 'WARNING');
    die('Access denied.');
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
        logSecurityEvent('CSRF Attack', 'Invalid CSRF token on registration', 'WARNING');
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $firstName = sanitizeInput($_POST['first_name'] ?? '');
        $lastName = sanitizeInput($_POST['last_name'] ?? '');

        // Validation
        if (empty($username) || empty($email) || empty($password) || empty($confirmPassword) || empty($firstName) || empty($lastName)) {
            $error = 'Please fill in all required fields.';
        } elseif (!validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } elseif (!validatePassword($password)) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long and contain uppercase, lowercase, and numeric characters.';
        } else {
            // Check if username or email already exists
            global $db;
            try {
                $stmt = $db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $email]);
                
                if ($stmt->fetch()) {
                    $error = 'Username or email already exists.';
                } else {
                    // Create new user
                    $userData = [
                        'username' => $username,
                        'email' => $email,
                        'password' => $password,
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'is_active' => 1,
                        'tenant_id' => 1 // Default tenant for now
                    ];
                    
                    $result = createUser($userData);
                    
                    if ($result['success']) {
                        $success = 'Account created successfully! You can now sign in.';
                        // Clear form data
                        $username = $email = $firstName = $lastName = '';
                    } else {
                        $error = $result['message'];
                    }
                }
            } catch (PDOException $e) {
                error_log("Registration error: " . $e->getMessage());
                $error = 'An error occurred during registration. Please try again.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Advanced Customs Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, rgba(43, 94, 95, 0.95), rgba(30, 74, 75, 0.95)),
                        url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
            opacity: 0.15;
            z-index: -2;
        }

        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(43, 94, 95, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 74, 75, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, rgba(43, 94, 95, 0.1) 0%, rgba(30, 74, 75, 0.1) 100%);
            z-index: -1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.25), 0 8px 24px rgba(43, 94, 95, 0.15);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 1;
            transform: translateZ(0);
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, rgba(43, 94, 95, 0.4), transparent, rgba(43, 94, 95, 0.4));
            z-index: -1;
            border-radius: 18px;
            opacity: 0.5;
        }

        .login-box {
            background: transparent;
        }

        .login-header {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%);
            padding: 40px 40px 25px;
            text-align: center;
            border-bottom: 1px solid rgba(226, 232, 240, 0.8);
            position: relative;
            backdrop-filter: blur(5px);
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
        }

        .login-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.2), transparent);
        }

        .logo-container {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.08), 0 2px 8px rgba(43, 94, 95, 0.1);
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .logo-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent, rgba(43, 94, 95, 0.05));
            z-index: 1;
        }

        .login-logo {
            width: 140px;
            height: auto;
            max-height: 80px;
            margin: 0 auto;
            display: block;
            filter: drop-shadow(0 2px 8px rgba(0,0,0,0.1));
            position: relative;
            z-index: 2;
        }

        .login-header h1 {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .login-header p {
            font-size: 15px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .industry-tagline {
            font-size: 12px;
            color: #94a3b8;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .login-form {
            padding: 35px 40px 40px;
            background: white;
            position: relative;
        }

        .login-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
        }

        .alert {
            padding: 14px 18px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 14px;
            border: 1px solid;
            font-weight: 500;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
            box-shadow: 0 2px 8px rgba(22, 163, 74, 0.1);
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 14px 18px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: #fafbfc;
            color: #374151;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            position: relative;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2B5E5F;
            background: white;
            box-shadow: 0 0 0 4px rgba(43, 94, 95, 0.1), 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .form-group input:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        .login-btn {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.8px;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #1e4a4b 0%, #163a3b 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(43, 94, 95, 0.4);
        }

        .login-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
        }

        .login-links {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: rgba(248, 250, 252, 0.5);
            border-top: 1px solid rgba(226, 232, 240, 0.8);
        }

        .login-links p {
            color: #64748b;
            font-size: 14px;
            margin: 0;
        }

        .register-link {
            color: #2B5E5F;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .register-link:hover {
            color: #1e4a4b;
            text-decoration: underline;
        }

        .login-footer {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 25px 40px;
            border-top: 1px solid #e2e8f0;
            font-size: 13px;
            color: #64748b;
            text-align: center;
            position: relative;
        }

        .login-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(43, 94, 95, 0.1), transparent);
        }

        /* Responsive design */
        @media (max-width: 480px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .form-row .form-group {
                margin-bottom: 20px;
            }

            .login-container {
                max-width: 100%;
                margin: 10px;
            }

            .login-header {
                padding: 30px 25px 20px;
            }

            .login-form {
                padding: 25px 25px 30px;
            }
        }

        /* Add subtle animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo-container">
                    <img src="assets/img/logo.png" alt="ACS Logo" class="login-logo">
                </div>
                <h1>Advanced Customs Solutions</h1>
                <p>Create Your Professional Account</p>
                <div class="industry-tagline">Join Our Global Trade Network</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="register.php" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" required
                               value="<?php echo htmlspecialchars($firstName ?? ''); ?>"
                               autocomplete="given-name">
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" required
                               value="<?php echo htmlspecialchars($lastName ?? ''); ?>"
                               autocomplete="family-name">
                    </div>
                </div>

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required
                           value="<?php echo htmlspecialchars($email ?? ''); ?>"
                           autocomplete="email">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required
                           autocomplete="new-password">
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required
                           autocomplete="new-password">
                </div>

                <button type="submit" class="login-btn">Create Account</button>
            </form>
            
            <div class="login-links">
                <p>Already have an account? <a href="login.php" class="register-link">Sign In</a></p>
            </div>
            
            <div class="login-footer">
                <p>&copy; <?php echo date('Y'); ?> ACS Freight & Accounting Portal. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script src="assets/js/login.js"></script>
</body>
</html>
