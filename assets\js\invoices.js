/**
 * Invoice Management JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeInvoiceManagement();
});

function initializeInvoiceManagement() {
    // Initialize form validation
    initializeInvoiceFormValidation();
    
    // Initialize auto-refresh for real-time updates
    initializeAutoRefresh();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

/**
 * Initialize invoice form validation
 */
function initializeInvoiceFormValidation() {
    const createForm = document.querySelector('#create-invoice-modal form');
    if (createForm) {
        createForm.addEventListener('submit', function(e) {
            if (!validateInvoiceForm(this)) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = createForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    }
}

/**
 * Validate invoice form
 */
function validateInvoiceForm(form) {
    let isValid = true;
    const formData = new FormData(form);
    
    // Validate invoice number
    const invoiceNumber = formData.get('invoice_number');
    if (!invoiceNumber || invoiceNumber.trim().length < 3) {
        showFieldError(form.querySelector('[name="invoice_number"]'), 'Invoice number must be at least 3 characters');
        isValid = false;
    }
    
    // Validate customer name
    const customerName = formData.get('customer_name');
    if (!customerName || customerName.trim().length < 2) {
        showFieldError(form.querySelector('[name="customer_name"]'), 'Customer name must be at least 2 characters');
        isValid = false;
    }
    
    // Validate email
    const customerEmail = formData.get('customer_email');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!customerEmail || !emailRegex.test(customerEmail)) {
        showFieldError(form.querySelector('[name="customer_email"]'), 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate amount
    const amount = parseFloat(formData.get('amount'));
    if (!amount || amount <= 0) {
        showFieldError(form.querySelector('[name="amount"]'), 'Amount must be greater than 0');
        isValid = false;
    }
    
    // Validate due date
    const dueDate = new Date(formData.get('due_date'));
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (dueDate < today) {
        showFieldError(form.querySelector('[name="due_date"]'), 'Due date cannot be in the past');
        isValid = false;
    }
    
    // Validate description
    const description = formData.get('description');
    if (!description || description.trim().length < 10) {
        showFieldError(form.querySelector('[name="description"]'), 'Description must be at least 10 characters');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    const name = field.name;
    
    switch (name) {
        case 'invoice_number':
            if (value.length < 3) {
                showFieldError(field, 'Invoice number must be at least 3 characters');
                return false;
            }
            break;
            
        case 'customer_name':
            if (value.length < 2) {
                showFieldError(field, 'Customer name must be at least 2 characters');
                return false;
            }
            break;
            
        case 'customer_email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                showFieldError(field, 'Please enter a valid email address');
                return false;
            }
            break;
            
        case 'amount':
            const amount = parseFloat(value);
            if (!amount || amount <= 0) {
                showFieldError(field, 'Amount must be greater than 0');
                return false;
            }
            break;
            
        case 'due_date':
            const dueDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (dueDate < today) {
                showFieldError(field, 'Due date cannot be in the past');
                return false;
            }
            break;
            
        case 'description':
            if (value.length < 10) {
                showFieldError(field, 'Description must be at least 10 characters');
                return false;
            }
            break;
    }
    
    clearFieldError(field);
    return true;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('error');
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * Initialize auto-refresh for real-time updates
 */
function initializeAutoRefresh() {
    // Refresh statistics every 30 seconds
    setInterval(function() {
        refreshInvoiceStatistics();
    }, 30000);
}

/**
 * Refresh invoice statistics
 */
function refreshInvoiceStatistics() {
    fetch('ajax/get_statistics.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatisticsDisplay(data.statistics);
            }
        })
        .catch(error => {
            console.error('Failed to refresh statistics:', error);
        });
}

/**
 * Update statistics display
 */
function updateStatisticsDisplay(stats) {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        const numberElement = card.querySelector('.stat-number');
        if (numberElement) {
            switch (index) {
                case 0:
                    numberElement.textContent = stats.total_invoices;
                    break;
                case 1:
                    numberElement.textContent = stats.paid_invoices;
                    break;
                case 2:
                    numberElement.textContent = stats.pending_invoices;
                    break;
                case 3:
                    numberElement.textContent = formatCurrency(stats.outstanding_amount);
                    break;
            }
        }
    });
}

/**
 * Initialize keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+N or Cmd+N to create new invoice
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const createButton = document.querySelector('[data-modal="create-invoice-modal"]');
            if (createButton) {
                createButton.click();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
}

/**
 * View invoice details
 */
function viewInvoice(invoiceId) {
    // Implementation for viewing invoice details
    window.open(`view.php?id=${invoiceId}`, '_blank');
}

/**
 * Edit invoice
 */
function editInvoice(invoiceId) {
    // Implementation for editing invoice
    window.location.href = `edit.php?id=${invoiceId}`;
}

/**
 * Download invoice PDF
 */
function downloadPDF(invoiceId) {
    // Implementation for downloading PDF
    window.open(`pdf.php?id=${invoiceId}`, '_blank');
}

/**
 * Mark invoice as paid
 */
function markAsPaid(invoiceId) {
    if (confirm('Are you sure you want to mark this invoice as paid?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="mark_paid">
            <input type="hidden" name="invoice_id" value="${invoiceId}">
            <input type="hidden" name="csrf_token" value="${getCSRFToken()}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

/**
 * Export invoices
 */
function exportInvoices() {
    const currentUrl = new URL(window.location);
    const params = new URLSearchParams(currentUrl.search);
    params.set('export', 'csv');
    
    window.open(`export.php?${params.toString()}`, '_blank');
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR'
    }).format(amount);
}

/**
 * Get CSRF token
 */
function getCSRFToken() {
    const tokenInput = document.querySelector('input[name="csrf_token"]');
    return tokenInput ? tokenInput.value : '';
}

/**
 * Generate invoice number
 */
function generateInvoiceNumber() {
    const year = new Date().getFullYear();
    const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    return `INV-${year}-${random}`;
}

/**
 * Auto-generate invoice number when modal opens
 */
document.addEventListener('modalOpened', function(e) {
    if (e.detail.modalId === 'create-invoice-modal') {
        const invoiceNumberField = document.querySelector('#invoice_number');
        if (invoiceNumberField && !invoiceNumberField.value) {
            invoiceNumberField.value = generateInvoiceNumber();
        }
        
        // Set default due date to 30 days from now
        const dueDateField = document.querySelector('#due_date');
        if (dueDateField && !dueDateField.value) {
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 30);
            dueDateField.value = futureDate.toISOString().split('T')[0];
        }
    }
});

/**
 * Real-time search functionality
 */
function initializeRealTimeSearch() {
    const searchInput = document.querySelector('#search');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value);
            }, 500);
        });
    }
}

/**
 * Perform search
 */
function performSearch(query) {
    const currentUrl = new URL(window.location);
    const params = new URLSearchParams(currentUrl.search);
    
    if (query.trim()) {
        params.set('search', query);
    } else {
        params.delete('search');
    }
    
    params.delete('page'); // Reset to first page
    
    window.location.href = `${currentUrl.pathname}?${params.toString()}`;
}

// Initialize real-time search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeRealTimeSearch();
});
