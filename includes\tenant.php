<?php
/**
 * Multi-Tenant Core Architecture - Freight & Accounting Portal
 * Handles tenant isolation, data segregation, and tenant-specific operations
 */

// Prevent direct access
if (!defined('PORTAL_ACCESS')) {
    define('PORTAL_ACCESS', true);
}

/**
 * Get current tenant context
 */
function getCurrentTenantContext() {
    if (!isLoggedIn()) {
        return null;
    }
    
    // Admin users can switch tenant context
    if (isAdmin() && isset($_SESSION['admin_tenant_context'])) {
        return $_SESSION['admin_tenant_context'];
    }
    
    return getCurrentTenantId();
}

/**
 * Set admin tenant context (admin only)
 */
function setAdminTenantContext($tenantId) {
    if (!isAdmin()) {
        return false;
    }
    
    // Validate tenant exists
    $tenant = getTenant($tenantId);
    if (!$tenant) {
        return false;
    }
    
    $_SESSION['admin_tenant_context'] = $tenantId;
    logActivity($_SESSION['user_id'], 'admin_context_switch', "Switched to tenant context: {$tenant['name']}");
    
    return true;
}

/**
 * Clear admin tenant context
 */
function clearAdminTenantContext() {
    if (isset($_SESSION['admin_tenant_context'])) {
        unset($_SESSION['admin_tenant_context']);
        logActivity($_SESSION['user_id'], 'admin_context_clear', 'Cleared admin tenant context');
    }
}

/**
 * Ensure tenant isolation in queries
 */
function addTenantFilter($query, $tenantAlias = 't') {
    $tenantId = getCurrentTenantContext();
    
    if (!$tenantId) {
        throw new Exception('No tenant context available');
    }
    
    // Add tenant filter to WHERE clause
    if (stripos($query, 'WHERE') !== false) {
        $query .= " AND {$tenantAlias}.tenant_id = {$tenantId}";
    } else {
        $query .= " WHERE {$tenantAlias}.tenant_id = {$tenantId}";
    }
    
    return $query;
}

/**
 * Validate tenant access for data
 */
function validateTenantAccess($tenantId, $resourceType = 'general') {
    $currentTenantId = getCurrentTenantContext();
    
    // Admin users have access to all tenants
    if (isAdmin()) {
        return true;
    }
    
    // Regular users can only access their own tenant data
    if ($currentTenantId != $tenantId) {
        logSecurityEvent('Tenant Access Violation', 
            "User {$_SESSION['user_id']} attempted to access tenant {$tenantId} data (resource: {$resourceType})", 
            'WARNING');
        return false;
    }
    
    return true;
}

/**
 * Get tenant-specific invoices
 */
function getTenantInvoices($tenantId = null, $limit = 50, $offset = 0) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'invoices')) {
        return [];
    }
    
    try {
        $stmt = $db->prepare("
            SELECT i.*, p.amount as paid_amount, p.payment_date
            FROM invoices i
            LEFT JOIN payments p ON i.id = p.invoice_id
            WHERE i.tenant_id = ?
            ORDER BY i.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$tenantId, $limit, $offset]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get tenant invoices: " . $e->getMessage());
        return [];
    }
}

/**
 * Get tenant-specific users
 */
function getTenantUsers($tenantId = null) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'users')) {
        return [];
    }
    
    try {
        $stmt = $db->prepare("
            SELECT u.*, GROUP_CONCAT(r.name) as roles
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            WHERE u.tenant_id = ? AND u.is_active = 1
            GROUP BY u.id
            ORDER BY u.username
        ");
        $stmt->execute([$tenantId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get tenant users: " . $e->getMessage());
        return [];
    }
}

/**
 * Get tenant statistics
 */
function getTenantStatistics($tenantId = null) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'statistics')) {
        return [];
    }
    
    try {
        $stats = [];
        
        // Total invoices
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE tenant_id = ?");
        $stmt->execute([$tenantId]);
        $stats['total_invoices'] = $stmt->fetch()['count'];
        
        // Paid invoices
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE tenant_id = ? AND status = 'paid'");
        $stmt->execute([$tenantId]);
        $stats['paid_invoices'] = $stmt->fetch()['count'];
        
        // Pending invoices
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE tenant_id = ? AND status = 'pending'");
        $stmt->execute([$tenantId]);
        $stats['pending_invoices'] = $stmt->fetch()['count'];
        
        // Total revenue
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM invoices WHERE tenant_id = ? AND status = 'paid'");
        $stmt->execute([$tenantId]);
        $stats['total_revenue'] = $stmt->fetch()['total'];
        
        // Outstanding amount
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM invoices WHERE tenant_id = ? AND status = 'pending'");
        $stmt->execute([$tenantId]);
        $stats['outstanding_amount'] = $stmt->fetch()['total'];
        
        // Active users
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND is_active = 1");
        $stmt->execute([$tenantId]);
        $stats['active_users'] = $stmt->fetch()['count'];
        
        return $stats;
    } catch (PDOException $e) {
        error_log("Failed to get tenant statistics: " . $e->getMessage());
        return [];
    }
}

/**
 * Get tenant FTP configuration
 */
function getTenantFTPConfig($tenantId = null) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'ftp_config')) {
        return null;
    }
    
    try {
        $stmt = $db->prepare("
            SELECT ftp_host, ftp_username, ftp_password, ftp_folder
            FROM tenants
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$tenantId]);
        $config = $stmt->fetch();
        
        if ($config && $config['ftp_password']) {
            // Decrypt FTP password (assuming it's hashed, you might want to use encryption instead)
            // For now, we'll return it as-is since we hashed it during storage
            // In production, use proper encryption/decryption
        }
        
        return $config;
    } catch (PDOException $e) {
        error_log("Failed to get tenant FTP config: " . $e->getMessage());
        return null;
    }
}

/**
 * Create tenant-specific invoice
 */
function createTenantInvoice($invoiceData, $tenantId = null) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'invoice_create')) {
        return ['success' => false, 'message' => 'Access denied'];
    }
    
    try {
        // Ensure tenant_id is set
        $invoiceData['tenant_id'] = $tenantId;
        
        $stmt = $db->prepare("
            INSERT INTO invoices (tenant_id, invoice_number, customer_name, customer_email, 
                                amount, due_date, description, status, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
        ");
        
        $result = $stmt->execute([
            $tenantId,
            $invoiceData['invoice_number'],
            $invoiceData['customer_name'],
            $invoiceData['customer_email'],
            $invoiceData['amount'],
            $invoiceData['due_date'],
            $invoiceData['description'],
            $_SESSION['user_id']
        ]);
        
        if ($result) {
            $invoiceId = $db->lastInsertId();
            logActivity($_SESSION['user_id'], 'invoice_created', "Created invoice: {$invoiceData['invoice_number']}");
            return ['success' => true, 'invoice_id' => $invoiceId];
        } else {
            return ['success' => false, 'message' => 'Failed to create invoice'];
        }
        
    } catch (PDOException $e) {
        error_log("Failed to create tenant invoice: " . $e->getMessage());
        return ['success' => false, 'message' => 'Database error occurred'];
    }
}

/**
 * Get tenant activity logs
 */
function getTenantActivityLogs($tenantId = null, $limit = 50) {
    global $db;
    
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'activity_logs')) {
        return [];
    }
    
    try {
        $stmt = $db->prepare("
            SELECT al.*, u.username
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE u.tenant_id = ? OR al.tenant_id = ?
            ORDER BY al.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$tenantId, $tenantId, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get tenant activity logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Validate tenant data ownership
 */
function validateDataOwnership($table, $recordId, $tenantIdColumn = 'tenant_id') {
    global $db;
    
    $currentTenantId = getCurrentTenantContext();
    
    if (!$currentTenantId) {
        return false;
    }
    
    // Admin users have access to all data
    if (isAdmin()) {
        return true;
    }
    
    try {
        $stmt = $db->prepare("SELECT {$tenantIdColumn} FROM {$table} WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch();
        
        if (!$record) {
            return false;
        }
        
        return $record[$tenantIdColumn] == $currentTenantId;
    } catch (PDOException $e) {
        error_log("Failed to validate data ownership: " . $e->getMessage());
        return false;
    }
}

/**
 * Get tenant dashboard data
 */
function getTenantDashboardData($tenantId = null) {
    $tenantId = $tenantId ?? getCurrentTenantContext();
    
    if (!validateTenantAccess($tenantId, 'dashboard')) {
        return null;
    }
    
    return [
        'tenant' => getTenant($tenantId),
        'statistics' => getTenantStatistics($tenantId),
        'recent_invoices' => getTenantInvoices($tenantId, 5),
        'recent_activity' => getTenantActivityLogs($tenantId, 10),
        'users' => getTenantUsers($tenantId)
    ];
}
