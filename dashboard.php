<?php
/**
 * Dashboard - Advanced Customs Solutions Portal
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/session.php';
require_once 'includes/auth.php';

// Initialize secure session
initSecureSession();

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Advanced Customs Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #1e293b;
        }

        .header {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 12px rgba(43, 94, 95, 0.3);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: auto;
            filter: brightness(0) invert(1);
        }

        .company-info h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .company-info p {
            font-size: 14px;
            opacity: 0.9;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            text-align: right;
        }

        .user-info h3 {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .user-info p {
            font-size: 12px;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .main-content {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .welcome-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .welcome-section h2 {
            font-size: 32px;
            color: #2B5E5F;
            margin-bottom: 16px;
        }

        .welcome-section p {
            font-size: 18px;
            color: #64748b;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-card .icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .stat-card h3 {
            font-size: 24px;
            color: #2B5E5F;
            margin-bottom: 8px;
        }

        .stat-card p {
            color: #64748b;
            font-size: 14px;
        }

        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .quick-actions h3 {
            font-size: 20px;
            color: #2B5E5F;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(43, 94, 95, 0.4);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .user-section {
                flex-direction: column;
                gap: 10px;
            }

            .welcome-section {
                padding: 25px;
            }

            .welcome-section h2 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="assets/img/logo.png" alt="ACS Logo" class="logo">
                <div class="company-info">
                    <h1>Advanced Customs Solutions</h1>
                    <p>Professional Import/Export Clearing Portal</p>
                </div>
            </div>
            <div class="user-section">
                <div class="user-info">
                    <h3>Welcome, <?php echo htmlspecialchars($_SESSION['username'] ?? 'User'); ?></h3>
                    <p>Last login: <?php echo date('M j, Y g:i A'); ?></p>
                </div>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <section class="welcome-section">
            <h2>Welcome to Your Dashboard</h2>
            <p>Manage your import/export operations with our comprehensive customs clearing platform</p>
        </section>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">📦</div>
                <h3>Active Shipments</h3>
                <p>Track your current shipments</p>
            </div>
            <div class="stat-card">
                <div class="icon">📋</div>
                <h3>Pending Clearances</h3>
                <p>Documents awaiting processing</p>
            </div>
            <div class="stat-card">
                <div class="icon">✅</div>
                <h3>Completed This Month</h3>
                <p>Successfully cleared shipments</p>
            </div>
            <div class="stat-card">
                <div class="icon">🌍</div>
                <h3>Global Partners</h3>
                <p>Worldwide network coverage</p>
            </div>
        </div>

        <section class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="action-buttons">
                <a href="#" class="action-btn">New Shipment</a>
                <a href="#" class="action-btn">Track Shipment</a>
                <a href="#" class="action-btn">Upload Documents</a>
                <a href="#" class="action-btn">View Reports</a>
                <a href="#" class="action-btn">Contact Support</a>
                <a href="#" class="action-btn">Settings</a>
            </div>
        </section>
    </main>
</body>
</html>
