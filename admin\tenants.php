<?php
/**
 * Tenant Management - Freight & Accounting Portal
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/session.php';
require_once '../includes/auth.php';

// Require admin privileges
requireAdmin();

$currentUser = getCurrentUser();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_tenant':
                handleCreateTenant();
                break;
            case 'update_tenant':
                handleUpdateTenant();
                break;
            case 'deactivate_tenant':
                handleDeactivateTenant();
                break;
        }
    }
}

// Get tenants list
$tenants = getAllTenants();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tenant Management - Admin Portal</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>Tenant Management</h1>
                <div class="user-info">
                    <a href="index.php" class="btn btn-secondary">Back to Admin</a>
                    <a href="../logout.php" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <main class="main-content">
            <?php if ($flash = getFlashMessage()): ?>
                <div class="alert alert-<?php echo $flash['type']; ?>">
                    <?php echo htmlspecialchars($flash['text']); ?>
                </div>
            <?php endif; ?>

            <div class="admin-section">
                <div class="section-header">
                    <h2>Client Tenants</h2>
                    <button class="btn btn-primary" data-modal="create-tenant-modal">Add New Tenant</button>
                </div>

                <div class="tenants-table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact Person</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tenants as $tenant): ?>
                            <tr>
                                <td><?php echo $tenant['id']; ?></td>
                                <td><?php echo htmlspecialchars($tenant['name']); ?></td>
                                <td><?php echo htmlspecialchars($tenant['contact_person']); ?></td>
                                <td><?php echo htmlspecialchars($tenant['contact_email']); ?></td>
                                <td><?php echo htmlspecialchars($tenant['contact_phone']); ?></td>
                                <td><?php echo $tenant['user_count']; ?></td>
                                <td>
                                    <span class="badge <?php echo $tenant['is_active'] ? 'badge-success' : 'badge-danger'; ?>">
                                        <?php echo $tenant['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($tenant['created_at'], 'M j, Y'); ?></td>
                                <td class="actions">
                                    <button class="btn btn-sm btn-primary" onclick="editTenant(<?php echo $tenant['id']; ?>)">Edit</button>
                                    <button class="btn btn-sm btn-info" onclick="viewTenantDetails(<?php echo $tenant['id']; ?>)">Details</button>
                                    <?php if ($tenant['is_active']): ?>
                                    <button class="btn btn-sm btn-warning" onclick="deactivateTenant(<?php echo $tenant['id']; ?>)">Deactivate</button>
                                    <?php else: ?>
                                    <button class="btn btn-sm btn-success" onclick="activateTenant(<?php echo $tenant['id']; ?>)">Activate</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Create Tenant Modal -->
    <div id="create-tenant-modal" class="modal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Tenant</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form method="POST" class="modal-body">
                <input type="hidden" name="action" value="create_tenant">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="name">Company Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="contact_person">Contact Person</label>
                    <input type="text" id="contact_person" name="contact_person" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contact_email">Contact Email</label>
                        <input type="email" id="contact_email" name="contact_email" required>
                    </div>
                    <div class="form-group">
                        <label for="contact_phone">Contact Phone</label>
                        <input type="tel" id="contact_phone" name="contact_phone">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="address">Address</label>
                    <textarea id="address" name="address" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="ftp_host">FTP Host</label>
                        <input type="text" id="ftp_host" name="ftp_host">
                    </div>
                    <div class="form-group">
                        <label for="ftp_username">FTP Username</label>
                        <input type="text" id="ftp_username" name="ftp_username">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="ftp_password">FTP Password</label>
                        <input type="password" id="ftp_password" name="ftp_password">
                    </div>
                    <div class="form-group">
                        <label for="ftp_folder">FTP Folder</label>
                        <input type="text" id="ftp_folder" name="ftp_folder" placeholder="/invoices/">
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-close">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Tenant</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function editTenant(tenantId) {
            // Implementation for edit tenant modal
            alert('Edit tenant functionality will be implemented');
        }
        
        function viewTenantDetails(tenantId) {
            // Implementation for tenant details modal
            alert('Tenant details functionality will be implemented');
        }
        
        function deactivateTenant(tenantId) {
            if (confirm('Are you sure you want to deactivate this tenant? This will disable access for all users.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="deactivate_tenant">
                    <input type="hidden" name="tenant_id" value="${tenantId}">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function activateTenant(tenantId) {
            if (confirm('Are you sure you want to activate this tenant?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="activate_tenant">
                    <input type="hidden" name="tenant_id" value="${tenantId}">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>

<?php
/**
 * Handle tenant creation
 */
function handleCreateTenant() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('tenants.php', 'Invalid request', 'error');
        return;
    }
    
    $tenantData = [
        'name' => sanitizeInput($_POST['name'] ?? ''),
        'contact_person' => sanitizeInput($_POST['contact_person'] ?? ''),
        'contact_email' => sanitizeInput($_POST['contact_email'] ?? ''),
        'contact_phone' => sanitizeInput($_POST['contact_phone'] ?? ''),
        'address' => sanitizeInput($_POST['address'] ?? ''),
        'ftp_host' => sanitizeInput($_POST['ftp_host'] ?? ''),
        'ftp_username' => sanitizeInput($_POST['ftp_username'] ?? ''),
        'ftp_password' => $_POST['ftp_password'] ?? '',
        'ftp_folder' => sanitizeInput($_POST['ftp_folder'] ?? '')
    ];
    
    $result = createTenant($tenantData);
    
    if ($result['success']) {
        logActivity($_SESSION['user_id'], 'tenant_created', "Created tenant: {$tenantData['name']}");
        redirectWithMessage('tenants.php', 'Tenant created successfully', 'success');
    } else {
        redirectWithMessage('tenants.php', $result['message'], 'error');
    }
}

/**
 * Handle tenant deactivation
 */
function handleDeactivateTenant() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('tenants.php', 'Invalid request', 'error');
        return;
    }
    
    $tenantId = (int)($_POST['tenant_id'] ?? 0);
    
    global $db;
    try {
        $stmt = $db->prepare("UPDATE tenants SET is_active = 0 WHERE id = ?");
        $stmt->execute([$tenantId]);
        
        logActivity($_SESSION['user_id'], 'tenant_deactivated', "Deactivated tenant ID: $tenantId");
        redirectWithMessage('tenants.php', 'Tenant deactivated successfully', 'success');
    } catch (PDOException $e) {
        error_log("Failed to deactivate tenant: " . $e->getMessage());
        redirectWithMessage('tenants.php', 'Failed to deactivate tenant', 'error');
    }
}

/**
 * Create new tenant
 */
function createTenant($tenantData) {
    global $db;
    
    try {
        // Validate required fields
        $required = ['name', 'contact_person', 'contact_email'];
        foreach ($required as $field) {
            if (empty($tenantData[$field])) {
                return ['success' => false, 'message' => "Field '$field' is required"];
            }
        }
        
        // Check if tenant name already exists
        $stmt = $db->prepare("SELECT id FROM tenants WHERE name = ?");
        $stmt->execute([$tenantData['name']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Tenant name already exists'];
        }
        
        // Insert tenant
        $stmt = $db->prepare("
            INSERT INTO tenants (name, contact_person, contact_email, contact_phone, address, 
                               ftp_host, ftp_username, ftp_password, ftp_folder, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $tenantData['name'],
            $tenantData['contact_person'],
            $tenantData['contact_email'],
            $tenantData['contact_phone'],
            $tenantData['address'],
            $tenantData['ftp_host'],
            $tenantData['ftp_username'],
            $tenantData['ftp_password'] ? hashPassword($tenantData['ftp_password']) : null,
            $tenantData['ftp_folder']
        ]);
        
        if ($result) {
            return ['success' => true, 'message' => 'Tenant created successfully', 'tenant_id' => $db->lastInsertId()];
        } else {
            return ['success' => false, 'message' => 'Failed to create tenant'];
        }
        
    } catch (PDOException $e) {
        error_log("Failed to create tenant: " . $e->getMessage());
        return ['success' => false, 'message' => 'Database error occurred'];
    }
}

/**
 * Get all tenants with user count
 */
function getAllTenants() {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT t.*, COUNT(u.id) as user_count
            FROM tenants t
            LEFT JOIN users u ON t.id = u.tenant_id AND u.is_active = 1
            GROUP BY t.id
            ORDER BY t.created_at DESC
        ");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get tenants: " . $e->getMessage());
        return [];
    }
}
?>
