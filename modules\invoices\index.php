<?php
/**
 * Invoice Management Module - Freight & Accounting Portal
 */

// Include configuration and core files
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';
require_once '../../includes/session.php';
require_once '../../includes/auth.php';
require_once '../../includes/tenant.php';

// Check authentication and permissions
if (!isLoggedIn()) {
    header('Location: ../../login.php');
    exit();
}

$currentUser = getCurrentUser();
$userPermissions = getUserPermissions($currentUser['id']);

if (!checkUserPermission($userPermissions, 'invoices')) {
    header('Location: ../../index.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_invoice':
                handleCreateInvoice();
                break;
            case 'update_invoice':
                handleUpdateInvoice();
                break;
            case 'delete_invoice':
                handleDeleteInvoice();
                break;
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Get invoices
$invoices = getFilteredInvoices($status, $search, $limit, $offset);
$totalInvoices = getTotalInvoicesCount($status, $search);
$totalPages = ceil($totalInvoices / $limit);

// Get tenant statistics
$tenantStats = getTenantStatistics();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Management - Freight & Accounting Portal</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="stylesheet" href="../../assets/css/invoices.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>Invoice Management</h1>
                <div class="user-info">
                    <a href="../../index.php" class="btn btn-secondary">Back to Dashboard</a>
                    <span>Welcome, <?php echo htmlspecialchars($currentUser['username']); ?></span>
                    <a href="../../logout.php" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <main class="main-content">
            <?php if ($flash = getFlashMessage()): ?>
                <div class="alert alert-<?php echo $flash['type']; ?>">
                    <?php echo htmlspecialchars($flash['text']); ?>
                </div>
            <?php endif; ?>

            <!-- Invoice Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📄</div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $tenantStats['total_invoices']; ?></div>
                        <div class="stat-label">Total Invoices</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $tenantStats['paid_invoices']; ?></div>
                        <div class="stat-label">Paid Invoices</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $tenantStats['pending_invoices']; ?></div>
                        <div class="stat-label">Pending Invoices</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo formatCurrency($tenantStats['outstanding_amount']); ?></div>
                        <div class="stat-label">Outstanding Amount</div>
                    </div>
                </div>
            </div>

            <!-- Invoice Management Section -->
            <div class="invoice-section">
                <div class="section-header">
                    <h2>Invoices</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" data-modal="create-invoice-modal">Create Invoice</button>
                        <button class="btn btn-secondary" onclick="exportInvoices()">Export</button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label for="status">Status:</label>
                            <select name="status" id="status">
                                <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All</option>
                                <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="paid" <?php echo $status === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                <option value="overdue" <?php echo $status === 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                                <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="search">Search:</label>
                            <input type="text" name="search" id="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Invoice number, customer...">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="index.php" class="btn btn-secondary">Clear</a>
                    </form>
                </div>

                <!-- Invoice Table -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                <td><?php echo formatCurrency($invoice['amount']); ?></td>
                                <td><?php echo formatDate($invoice['due_date'], 'M j, Y'); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo getStatusBadgeClass($invoice['status']); ?>">
                                        <?php echo ucfirst($invoice['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($invoice['created_at'], 'M j, Y'); ?></td>
                                <td class="actions">
                                    <button class="btn btn-sm btn-primary" onclick="viewInvoice(<?php echo $invoice['id']; ?>)">View</button>
                                    <button class="btn btn-sm btn-secondary" onclick="editInvoice(<?php echo $invoice['id']; ?>)">Edit</button>
                                    <button class="btn btn-sm btn-info" onclick="downloadPDF(<?php echo $invoice['id']; ?>)">PDF</button>
                                    <?php if ($invoice['status'] === 'pending'): ?>
                                    <button class="btn btn-sm btn-success" onclick="markAsPaid(<?php echo $invoice['id']; ?>)">Mark Paid</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>" class="btn btn-secondary">Previous</a>
                    <?php endif; ?>
                    
                    <span class="page-info">Page <?php echo $page; ?> of <?php echo $totalPages; ?></span>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>" class="btn btn-secondary">Next</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Create Invoice Modal -->
    <div id="create-invoice-modal" class="modal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Invoice</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form method="POST" class="modal-body">
                <input type="hidden" name="action" value="create_invoice">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoice_number">Invoice Number</label>
                        <input type="text" id="invoice_number" name="invoice_number" value="INV-<?php echo date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="due_date">Due Date</label>
                        <input type="date" id="due_date" name="due_date" value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="customer_name">Customer Name</label>
                        <input type="text" id="customer_name" name="customer_name" required>
                    </div>
                    <div class="form-group">
                        <label for="customer_email">Customer Email</label>
                        <input type="email" id="customer_email" name="customer_email" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="amount">Amount</label>
                    <input type="number" id="amount" name="amount" step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="3" required></textarea>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-close">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Invoice</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/admin.js"></script>
    <script src="../../assets/js/invoices.js"></script>
</body>
</html>

<?php
/**
 * Get filtered invoices
 */
function getFilteredInvoices($status, $search, $limit, $offset) {
    global $db;
    
    $tenantId = getCurrentTenantContext();
    if (!$tenantId) return [];
    
    $whereConditions = ["i.tenant_id = ?"];
    $params = [$tenantId];
    
    if ($status !== 'all') {
        if ($status === 'overdue') {
            $whereConditions[] = "i.status = 'pending' AND i.due_date < CURDATE()";
        } else {
            $whereConditions[] = "i.status = ?";
            $params[] = $status;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(i.invoice_number LIKE ? OR i.customer_name LIKE ? OR i.customer_email LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    try {
        $stmt = $db->prepare("
            SELECT i.*, u.username as created_by_name
            FROM invoices i
            LEFT JOIN users u ON i.created_by = u.id
            WHERE $whereClause
            ORDER BY i.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Failed to get filtered invoices: " . $e->getMessage());
        return [];
    }
}

/**
 * Get total invoices count
 */
function getTotalInvoicesCount($status, $search) {
    global $db;
    
    $tenantId = getCurrentTenantContext();
    if (!$tenantId) return 0;
    
    $whereConditions = ["tenant_id = ?"];
    $params = [$tenantId];
    
    if ($status !== 'all') {
        if ($status === 'overdue') {
            $whereConditions[] = "status = 'pending' AND due_date < CURDATE()";
        } else {
            $whereConditions[] = "status = ?";
            $params[] = $status;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(invoice_number LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    try {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE $whereClause");
        $stmt->execute($params);
        return $stmt->fetch()['count'];
    } catch (PDOException $e) {
        error_log("Failed to get total invoices count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get status badge class
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'paid': return 'success';
        case 'pending': return 'warning';
        case 'overdue': return 'danger';
        case 'cancelled': return 'secondary';
        default: return 'primary';
    }
}

/**
 * Handle invoice creation
 */
function handleCreateInvoice() {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirectWithMessage('index.php', 'Invalid request', 'error');
        return;
    }
    
    $invoiceData = [
        'invoice_number' => sanitizeInput($_POST['invoice_number'] ?? ''),
        'customer_name' => sanitizeInput($_POST['customer_name'] ?? ''),
        'customer_email' => sanitizeInput($_POST['customer_email'] ?? ''),
        'amount' => (float)($_POST['amount'] ?? 0),
        'due_date' => $_POST['due_date'] ?? '',
        'description' => sanitizeInput($_POST['description'] ?? '')
    ];
    
    $result = createTenantInvoice($invoiceData);
    
    if ($result['success']) {
        logActivity($_SESSION['user_id'], 'invoice_created', "Created invoice: {$invoiceData['invoice_number']}");
        redirectWithMessage('index.php', 'Invoice created successfully', 'success');
    } else {
        redirectWithMessage('index.php', $result['message'], 'error');
    }
}
?>
