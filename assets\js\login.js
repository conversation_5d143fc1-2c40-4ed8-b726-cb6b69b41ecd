/**
 * Login Page JavaScript - Freight & Accounting Portal
 */

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.querySelector('.login-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');
    
    // Focus on username field
    if (usernameInput) {
        usernameInput.focus();
    }
    
    // Handle form submission
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            if (!validateLoginForm()) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            showLoginLoading();
        });
    }
    
    // Real-time validation
    if (usernameInput) {
        usernameInput.addEventListener('blur', function() {
            validateUsername();
        });
        
        usernameInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    if (passwordInput) {
        passwordInput.addEventListener('blur', function() {
            validatePassword();
        });
        
        passwordInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    // Handle Enter key navigation
    if (usernameInput) {
        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (passwordInput) {
                    passwordInput.focus();
                }
            }
        });
    }
    
    if (passwordInput) {
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (validateLoginForm()) {
                    loginForm.submit();
                }
            }
        });
    }
    
    // Validate login form
    function validateLoginForm() {
        let isValid = true;
        
        // Clear previous errors
        clearAllErrors();
        
        // Validate username
        if (!validateUsername()) {
            isValid = false;
        }
        
        // Validate password
        if (!validatePassword()) {
            isValid = false;
        }
        
        return isValid;
    }
    
    // Validate username field
    function validateUsername() {
        const username = usernameInput.value.trim();
        
        if (!username) {
            showFieldError(usernameInput, 'Username is required');
            return false;
        }
        
        if (username.length < 3) {
            showFieldError(usernameInput, 'Username must be at least 3 characters');
            return false;
        }
        
        // Check for valid characters (alphanumeric, underscore, hyphen)
        if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
            showFieldError(usernameInput, 'Username can only contain letters, numbers, underscore, and hyphen');
            return false;
        }
        
        return true;
    }
    
    // Validate password field
    function validatePassword() {
        const password = passwordInput.value;
        
        if (!password) {
            showFieldError(passwordInput, 'Password is required');
            return false;
        }
        
        if (password.length < 6) {
            showFieldError(passwordInput, 'Password must be at least 6 characters');
            return false;
        }
        
        return true;
    }
    
    // Show field error
    function showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }
    
    // Clear field error
    function clearFieldError(field) {
        field.classList.remove('error');
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    // Clear all errors
    function clearAllErrors() {
        const errorFields = document.querySelectorAll('.error');
        errorFields.forEach(field => {
            field.classList.remove('error');
        });
        
        const errorMessages = document.querySelectorAll('.field-error');
        errorMessages.forEach(error => {
            error.remove();
        });
    }
    
    // Show loading state
    function showLoginLoading() {
        if (loginBtn) {
            loginBtn.disabled = true;
            loginBtn.classList.add('loading');
            loginBtn.textContent = 'Signing In...';
        }
        
        // Disable form inputs
        if (usernameInput) usernameInput.disabled = true;
        if (passwordInput) passwordInput.disabled = true;
    }
    
    // Hide loading state (in case of error)
    function hideLoginLoading() {
        if (loginBtn) {
            loginBtn.disabled = false;
            loginBtn.classList.remove('loading');
            loginBtn.textContent = 'Sign In';
        }
        
        // Re-enable form inputs
        if (usernameInput) usernameInput.disabled = false;
        if (passwordInput) passwordInput.disabled = false;
    }
    
    // Handle login errors (if any)
    const alertError = document.querySelector('.alert-error');
    if (alertError) {
        hideLoginLoading();
        
        // Focus on username field for retry
        if (usernameInput) {
            usernameInput.focus();
            usernameInput.select();
        }
    }
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 300);
        }, 5000);
    });
    
    // Prevent multiple form submissions
    let isSubmitting = false;
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            if (isSubmitting) {
                e.preventDefault();
                return false;
            }
            isSubmitting = true;
        });
    }
    
    // Security: Clear password field on page unload
    window.addEventListener('beforeunload', function() {
        if (passwordInput) {
            passwordInput.value = '';
        }
    });
    
    // Caps Lock detection
    if (passwordInput) {
        passwordInput.addEventListener('keypress', function(e) {
            const capsLockOn = e.getModifierState && e.getModifierState('CapsLock');
            const capsWarning = document.getElementById('caps-warning');
            
            if (capsLockOn) {
                if (!capsWarning) {
                    const warning = document.createElement('div');
                    warning.id = 'caps-warning';
                    warning.className = 'caps-warning';
                    warning.textContent = 'Caps Lock is on';
                    passwordInput.parentNode.appendChild(warning);
                }
            } else {
                if (capsWarning) {
                    capsWarning.remove();
                }
            }
        });
    }
});

// Add CSS for field errors and caps warning
const style = document.createElement('style');
style.textContent = `
    .login-form input.error {
        border-color: #e74c3c !important;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
    }
    
    .field-error {
        color: #e74c3c;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        display: block;
    }
    
    .caps-warning {
        color: #f39c12;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        display: block;
    }
    
    .login-form .alert {
        animation: slideDown 0.3s ease-out;
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
