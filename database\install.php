<?php
/**
 * Database Installation Script - Freight & Accounting Portal
 * Run this script to create the database schema and initial data
 */

// Prevent direct access from web
if (php_sapi_name() !== 'cli' && !isset($_GET['install'])) {
    die('This script can only be run from command line or with ?install parameter');
}

// Include configuration
require_once __DIR__ . '/../config/config.php';

echo "Freight & Accounting Portal - Database Installation\n";
echo "==================================================\n\n";

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✓ Connected to MySQL server\n";
    
    // Read and execute schema file
    $schemaFile = __DIR__ . '/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    if ($schema === false) {
        throw new Exception("Failed to read schema file");
    }
    
    echo "✓ Schema file loaded\n";
    
    // Split SQL statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "✓ Found " . count($statements) . " SQL statements\n";
    
    // Execute each statement
    $executed = 0;
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                $executed++;
            } catch (PDOException $e) {
                // Ignore "database exists" errors
                if (strpos($e->getMessage(), 'database exists') === false) {
                    throw $e;
                }
            }
        }
    }
    
    echo "✓ Executed $executed SQL statements\n";
    
    // Verify installation
    $pdo->exec("USE " . DB_NAME);
    
    // Check if tables were created
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $expectedTables = [
        'tenants', 'users', 'roles', 'permissions', 'role_permissions',
        'user_roles', 'user_permissions', 'invoices', 'payment_transactions',
        'ftp_file_logs', 'activity_logs', 'login_attempts', 'system_settings'
    ];
    
    $missingTables = array_diff($expectedTables, $tables);
    if (!empty($missingTables)) {
        throw new Exception("Missing tables: " . implode(', ', $missingTables));
    }
    
    echo "✓ All required tables created\n";
    
    // Verify default data
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $tenantCount = $pdo->query("SELECT COUNT(*) FROM tenants")->fetchColumn();
    $roleCount = $pdo->query("SELECT COUNT(*) FROM roles")->fetchColumn();
    $permissionCount = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
    
    echo "✓ Default data inserted:\n";
    echo "  - Tenants: $tenantCount\n";
    echo "  - Users: $userCount\n";
    echo "  - Roles: $roleCount\n";
    echo "  - Permissions: $permissionCount\n";
    
    // Create additional system settings
    $settings = [
        ['app_name', 'Freight & Accounting Portal', 'Application name'],
        ['app_version', '1.0.0', 'Application version'],
        ['maintenance_mode', '0', 'Maintenance mode flag'],
        ['max_file_upload_size', '********', 'Maximum file upload size in bytes'],
        ['session_timeout', '3600', 'Session timeout in seconds'],
        ['password_min_length', '8', 'Minimum password length'],
        ['max_login_attempts', '5', 'Maximum login attempts before lockout'],
        ['login_lockout_time', '900', 'Login lockout time in seconds'],
        ['ftp_check_interval', '300', 'FTP folder check interval in seconds'],
        ['payment_gateway_default', 'payfast', 'Default payment gateway'],
        ['email_notifications', '1', 'Enable email notifications'],
        ['audit_log_retention', '365', 'Audit log retention in days']
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO system_settings (setting_key, setting_value, description) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "✓ System settings configured\n";
    
    // Create necessary directories
    $directories = [
        ROOT_PATH . '/logs',
        ROOT_PATH . '/uploads',
        ROOT_PATH . '/uploads/invoices',
        ROOT_PATH . '/uploads/documents',
        ROOT_PATH . '/temp',
        ROOT_PATH . '/ftp_data'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✓ Created directory: $dir\n";
            } else {
                echo "⚠ Failed to create directory: $dir\n";
            }
        }
    }
    
    // Create .htaccess files for security
    $htaccessContent = "Order Deny,Allow\nDeny from all\n";
    
    $protectedDirs = [
        ROOT_PATH . '/logs',
        ROOT_PATH . '/uploads',
        ROOT_PATH . '/temp',
        ROOT_PATH . '/config',
        ROOT_PATH . '/database'
    ];
    
    foreach ($protectedDirs as $dir) {
        $htaccessFile = $dir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, $htaccessContent);
            echo "✓ Created .htaccess for: $dir\n";
        }
    }
    
    echo "\n";
    echo "🎉 Database installation completed successfully!\n";
    echo "\n";
    echo "Default Login Credentials:\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "\n";
    echo "⚠ IMPORTANT: Please change the default password after first login!\n";
    echo "\n";
    echo "Next Steps:\n";
    echo "1. Configure your web server to point to the portal directory\n";
    echo "2. Update config/config.php with your specific settings\n";
    echo "3. Configure FTP settings for invoice processing\n";
    echo "4. Set up payment gateway credentials\n";
    echo "5. Configure email settings for notifications\n";
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Installation failed: " . $e->getMessage() . "\n";
    echo "\nPlease check your database configuration and try again.\n";
    exit(1);
}
?>
