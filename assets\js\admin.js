/**
 * Admin Interface JavaScript - Freight & Accounting Portal
 */

// Admin-specific functionality
const AdminPortal = {
    // Initialize admin interface
    init: function() {
        this.setupDataTables();
        this.setupFormValidation();
        this.setupPasswordGenerator();
        this.initializeCharts();
        this.setupAutoRefresh();
    },
    
    // Setup DataTables for better table management
    setupDataTables: function() {
        // Check if DataTables library is available
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('.table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: -1 } // Disable sorting on actions column
                ],
                language: {
                    search: "Search users:",
                    lengthMenu: "Show _MENU_ users per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ users",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        }
    },
    
    // Setup form validation
    setupFormValidation: function() {
        // Password strength validation
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        passwordInputs.forEach(input => {
            input.addEventListener('input', function() {
                AdminPortal.validatePasswordStrength(this);
            });
        });
        
        // Username validation
        const usernameInputs = document.querySelectorAll('input[name="username"]');
        usernameInputs.forEach(input => {
            input.addEventListener('blur', function() {
                AdminPortal.validateUsername(this);
            });
        });
        
        // Email validation
        const emailInputs = document.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            input.addEventListener('blur', function() {
                AdminPortal.validateEmail(this);
            });
        });
    },
    
    // Validate password strength
    validatePasswordStrength: function(input) {
        const password = input.value;
        const strengthIndicator = input.parentNode.querySelector('.password-strength') || 
                                 this.createPasswordStrengthIndicator(input);
        
        if (password.length === 0) {
            strengthIndicator.style.display = 'none';
            return;
        }
        
        let score = 0;
        let feedback = [];
        
        // Length check
        if (password.length >= 8) score++;
        else feedback.push('At least 8 characters');
        
        // Uppercase letter
        if (/[A-Z]/.test(password)) score++;
        else feedback.push('One uppercase letter');
        
        // Lowercase letter
        if (/[a-z]/.test(password)) score++;
        else feedback.push('One lowercase letter');
        
        // Number
        if (/[0-9]/.test(password)) score++;
        else feedback.push('One number');
        
        // Special character
        if (/[^A-Za-z0-9]/.test(password)) score++;
        else feedback.push('One special character');
        
        const strength = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'][score];
        const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#28a745'];
        
        strengthIndicator.style.display = 'block';
        strengthIndicator.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill" style="width: ${(score/5)*100}%; background: ${colors[score]}"></div>
            </div>
            <div class="strength-text">Strength: ${strength}</div>
            ${feedback.length > 0 ? `<div class="strength-feedback">Missing: ${feedback.join(', ')}</div>` : ''}
        `;
    },
    
    // Create password strength indicator
    createPasswordStrengthIndicator: function(input) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';
        input.parentNode.appendChild(indicator);
        return indicator;
    },
    
    // Validate username
    validateUsername: function(input) {
        const username = input.value.trim();
        const feedback = input.parentNode.querySelector('.validation-feedback') ||
                        this.createValidationFeedback(input);
        
        if (username.length === 0) {
            this.clearValidation(input, feedback);
            return;
        }
        
        let isValid = true;
        let message = '';
        
        if (username.length < 3) {
            isValid = false;
            message = 'Username must be at least 3 characters long';
        } else if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
            isValid = false;
            message = 'Username can only contain letters, numbers, underscore, and hyphen';
        }
        
        this.setValidation(input, feedback, isValid, message);
    },
    
    // Validate email
    validateEmail: function(input) {
        const email = input.value.trim();
        const feedback = input.parentNode.querySelector('.validation-feedback') ||
                        this.createValidationFeedback(input);
        
        if (email.length === 0) {
            this.clearValidation(input, feedback);
            return;
        }
        
        const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        const message = isValid ? '' : 'Please enter a valid email address';
        
        this.setValidation(input, feedback, isValid, message);
    },
    
    // Create validation feedback element
    createValidationFeedback: function(input) {
        const feedback = document.createElement('div');
        feedback.className = 'validation-feedback';
        input.parentNode.appendChild(feedback);
        return feedback;
    },
    
    // Set validation state
    setValidation: function(input, feedback, isValid, message) {
        input.classList.remove('is-valid', 'is-invalid');
        input.classList.add(isValid ? 'is-valid' : 'is-invalid');
        feedback.textContent = message;
        feedback.className = `validation-feedback ${isValid ? 'valid-feedback' : 'invalid-feedback'}`;
        feedback.style.display = message ? 'block' : 'none';
    },
    
    // Clear validation
    clearValidation: function(input, feedback) {
        input.classList.remove('is-valid', 'is-invalid');
        feedback.style.display = 'none';
    },
    
    // Setup password generator
    setupPasswordGenerator: function() {
        const generateButtons = document.querySelectorAll('.generate-password');
        generateButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const passwordInput = this.parentNode.querySelector('input[type="password"]');
                if (passwordInput) {
                    passwordInput.value = AdminPortal.generateSecurePassword();
                    AdminPortal.validatePasswordStrength(passwordInput);
                }
            });
        });
    },
    
    // Generate secure password
    generateSecurePassword: function(length = 12) {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        
        // Ensure at least one character from each category
        password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
        password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
        password += '0123456789'[Math.floor(Math.random() * 10)];
        password += '!@#$%^&*'[Math.floor(Math.random() * 8)];
        
        // Fill the rest randomly
        for (let i = 4; i < length; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }
        
        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    },
    
    // Initialize charts (if chart library is available)
    initializeCharts: function() {
        // This would integrate with Chart.js or similar library
        // For now, we'll just log that charts would be initialized
        console.log('Charts would be initialized here');
    },
    
    // Setup auto-refresh for real-time data
    setupAutoRefresh: function() {
        // Auto-refresh activity logs every 30 seconds
        if (document.querySelector('.activity-list')) {
            setInterval(() => {
                this.refreshActivityLogs();
            }, 30000);
        }
        
        // Auto-refresh statistics every 60 seconds
        if (document.querySelector('.stats-grid')) {
            setInterval(() => {
                this.refreshStatistics();
            }, 60000);
        }
    },
    
    // Refresh activity logs
    refreshActivityLogs: function() {
        FreightPortal.ajax('/admin/api/activity-logs.php')
            .then(data => {
                if (data.success) {
                    this.updateActivityLogs(data.logs);
                }
            })
            .catch(error => {
                console.error('Failed to refresh activity logs:', error);
            });
    },
    
    // Update activity logs in DOM
    updateActivityLogs: function(logs) {
        const activityList = document.querySelector('.activity-list');
        if (!activityList) return;
        
        activityList.innerHTML = logs.map(log => `
            <div class="activity-item">
                <div class="activity-icon">${this.getActivityIcon(log.action)}</div>
                <div class="activity-content">
                    <div class="activity-title">${log.description}</div>
                    <div class="activity-meta">
                        ${log.username ? `by ${log.username}` : ''} • ${this.formatDate(log.created_at)}
                    </div>
                </div>
            </div>
        `).join('');
    },
    
    // Refresh statistics
    refreshStatistics: function() {
        FreightPortal.ajax('/admin/api/statistics.php')
            .then(data => {
                if (data.success) {
                    this.updateStatistics(data.stats);
                }
            })
            .catch(error => {
                console.error('Failed to refresh statistics:', error);
            });
    },
    
    // Update statistics in DOM
    updateStatistics: function(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"] .stat-number`);
            if (element) {
                element.textContent = stats[key];
            }
        });
    },
    
    // Get activity icon
    getActivityIcon: function(action) {
        const icons = {
            'login': '🔐',
            'logout': '🚪',
            'user_created': '👤',
            'user_updated': '✏️',
            'user_deleted': '🗑️',
            'invoice_created': '📄',
            'payment_processed': '💰',
            'security_event': '⚠️',
            'system_setting': '⚙️'
        };
        return icons[action] || '📝';
    },
    
    // Format date
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit'
        });
    },
    
    // Bulk actions
    setupBulkActions: function() {
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const bulkActionButton = document.getElementById('bulk-action');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                AdminPortal.updateBulkActionButton();
            });
        }
        
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                AdminPortal.updateBulkActionButton();
            });
        });
    },
    
    // Update bulk action button state
    updateBulkActionButton: function() {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        const bulkActionButton = document.getElementById('bulk-action');
        
        if (bulkActionButton) {
            bulkActionButton.disabled = checkedBoxes.length === 0;
            bulkActionButton.textContent = `Actions (${checkedBoxes.length} selected)`;
        }
    }
};

// Initialize admin interface when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    AdminPortal.init();
});

// Export for use in other scripts
window.AdminPortal = AdminPortal;
