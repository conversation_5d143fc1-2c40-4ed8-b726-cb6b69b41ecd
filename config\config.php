<?php
/**
 * Configuration File - Freight & Accounting Portal
 */

// Prevent direct access
if (!defined('PORTAL_ACCESS')) {
    define('PORTAL_ACCESS', true);
}

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'freight_portal');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Freight & Accounting Portal');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/portal-new');
define('APP_TIMEZONE', 'Africa/Johannesburg');

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes in seconds

// File Upload Configuration
define('MAX_FILE_SIZE', ********); // 10MB in bytes
define('ALLOWED_FILE_TYPES', ['pdf', 'txt', 'xml', 'csv']);

// FTP Configuration
define('FTP_HOST', 'localhost');
define('FTP_PORT', 21);
define('FTP_USER', 'ftpuser');
define('FTP_PASS', 'ftppass');
define('FTP_ROOT_PATH', '/ftp_root/');
define('FTP_TIMEOUT', 30);

// Payment Gateway Configuration
// PayFast Configuration
define('PAYFAST_MERCHANT_ID', '');
define('PAYFAST_MERCHANT_KEY', '');
define('PAYFAST_PASSPHRASE', '');
define('PAYFAST_SANDBOX', true); // Set to false for production

// Peach Payments Configuration
define('PEACH_USER_ID', '');
define('PEACH_PASSWORD', '');
define('PEACH_ENTITY_ID', '');
define('PEACH_SANDBOX', true); // Set to false for production

// PayGate Configuration
define('PAYGATE_ID', '');
define('PAYGATE_SECRET', '');
define('PAYGATE_SANDBOX', true); // Set to false for production

// Email Configuration
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USER', '');
define('SMTP_PASS', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Freight Portal');

// Logging Configuration
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_SIZE', ********); // 10MB
define('LOG_MAX_FILES', 10);

// Directory Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('MODULES_PATH', ROOT_PATH . '/modules');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('TEMP_PATH', ROOT_PATH . '/temp');

// Create necessary directories if they don't exist
$directories = [
    LOG_FILE_PATH,
    UPLOADS_PATH,
    TEMP_PATH,
    UPLOADS_PATH . '/invoices',
    UPLOADS_PATH . '/documents'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Set timezone
date_default_timezone_set(APP_TIMEZONE);

// Error reporting (adjust for production)
if (defined('DEVELOPMENT') && DEVELOPMENT) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Database connection function
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    return $pdo;
}

// Initialize database connection
$db = getDBConnection();
?>
