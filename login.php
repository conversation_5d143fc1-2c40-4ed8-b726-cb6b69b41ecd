<?php
/**
 * Login Page - Freight & Accounting Portal
 */

// Include configuration and functions
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/session.php';
require_once 'includes/auth.php';

// Check for suspicious activity
if (detectSuspiciousActivity()) {
    logSecurityEvent('Suspicious Login Attempt', 'Suspicious activity detected on login page', 'WARNING');
}

// Check IP restrictions
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
if (isIPBlacklisted($clientIP)) {
    logSecurityEvent('Blocked IP Access', "Blocked IP attempted login: $clientIP", 'WARNING');
    die('Access denied.');
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
        logSecurityEvent('CSRF Attack', 'Invalid CSRF token on login', 'WARNING');
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';

        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } elseif (!checkRateLimit($username)) {
            $error = 'Too many login attempts. Please try again later.';
            logSecurityEvent('Rate Limit Exceeded', "Rate limit exceeded for username: $username", 'WARNING');
        } else {
            $loginResult = authenticateUser($username, $password);

            if ($loginResult['success']) {
                // Create secure session
                createUserSession($loginResult['user']);

                header('Location: index.php');
                exit();
            } else {
                $error = $loginResult['message'];
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Freight & Accounting Portal</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/login.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>Freight & Accounting Portal</h1>
                <p>Please sign in to your account</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="login.php" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required
                           autocomplete="current-password">
                </div>

                <button type="submit" class="login-btn">Sign In</button>
            </form>
            
            <div class="login-footer">
                <p>&copy; <?php echo date('Y'); ?> Freight & Accounting Portal. All rights reserved.</p>
            </div>
        </div>
    </div>
    
    <script src="assets/js/login.js"></script>
</body>
</html>
