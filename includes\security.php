<?php
/**
 * Security Functions - Freight & Accounting Portal
 */

// Prevent direct access
if (!defined('PORTAL_ACCESS')) {
    define('PORTAL_ACCESS', true);
}

/**
 * Sanitize input to prevent XSS attacks
 */
function sanitizeXSS($input) {
    if (is_array($input)) {
        return array_map('sanitizeXSS', $input);
    }
    
    // Remove null bytes
    $input = str_replace(chr(0), '', $input);
    
    // Remove carriage returns and line feeds
    $input = str_replace(["\r", "\n"], '', $input);
    
    // Convert special characters to HTML entities
    $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    return $input;
}

/**
 * Validate and sanitize SQL input
 */
function sanitizeSQL($input) {
    if (is_array($input)) {
        return array_map('sanitizeSQL', $input);
    }
    
    // Remove null bytes
    $input = str_replace(chr(0), '', $input);
    
    // Trim whitespace
    $input = trim($input);
    
    return $input;
}

/**
 * Generate secure random password
 */
function generateSecurePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    return $password;
}

/**
 * Check password strength
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // Length check
    if (strlen($password) >= 8) {
        $score += 1;
    } else {
        $feedback[] = 'Password should be at least 8 characters long';
    }
    
    // Uppercase letter
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain at least one uppercase letter';
    }
    
    // Lowercase letter
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain at least one lowercase letter';
    }
    
    // Number
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain at least one number';
    }
    
    // Special character
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Password should contain at least one special character';
    }
    
    // Common password check
    $commonPasswords = [
        'password', '123456', 'password123', 'admin', 'qwerty',
        'letmein', 'welcome', 'monkey', '1234567890'
    ];
    
    if (in_array(strtolower($password), $commonPasswords)) {
        $score = 0;
        $feedback[] = 'Password is too common';
    }
    
    $strength = 'Very Weak';
    if ($score >= 4) $strength = 'Strong';
    elseif ($score >= 3) $strength = 'Medium';
    elseif ($score >= 2) $strength = 'Weak';
    
    return [
        'score' => $score,
        'strength' => $strength,
        'feedback' => $feedback
    ];
}

/**
 * Rate limiting for login attempts
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE (username = ? OR ip_address = ?) 
            AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$identifier, $_SERVER['REMOTE_ADDR'], $timeWindow]);
        $result = $stmt->fetch();
        
        return $result['attempts'] < $maxAttempts;
    } catch (PDOException $e) {
        error_log("Rate limit check failed: " . $e->getMessage());
        return true; // Allow on error to prevent lockout
    }
}

/**
 * Log security event
 */
function logSecurityEvent($event, $details, $severity = 'INFO') {
    global $db;
    
    try {
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $userId = $_SESSION['user_id'] ?? null;
        $description = "[$severity] $event: $details";
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->execute([$userId, 'security_event', $description, $ipAddress, $userAgent]);
    } catch (PDOException $e) {
        error_log("Failed to log security event: " . $e->getMessage());
    }
}

/**
 * Detect suspicious activity
 */
function detectSuspiciousActivity() {
    $suspicious = false;
    $reasons = [];
    
    // Check for SQL injection patterns
    $inputs = array_merge($_GET, $_POST);
    foreach ($inputs as $key => $value) {
        if (is_string($value)) {
            $patterns = [
                '/union\s+select/i',
                '/drop\s+table/i',
                '/insert\s+into/i',
                '/delete\s+from/i',
                '/update\s+set/i',
                '/<script/i',
                '/javascript:/i',
                '/on\w+\s*=/i'
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    $suspicious = true;
                    $reasons[] = "Suspicious pattern detected in $key";
                    break;
                }
            }
        }
    }
    
    // Check for unusual request patterns
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if (empty($userAgent) || strlen($userAgent) < 10) {
        $suspicious = true;
        $reasons[] = "Missing or suspicious user agent";
    }
    
    // Check for rapid requests (basic bot detection)
    $sessionKey = 'last_request_time';
    $currentTime = microtime(true);
    if (isset($_SESSION[$sessionKey])) {
        $timeDiff = $currentTime - $_SESSION[$sessionKey];
        if ($timeDiff < 0.5) { // Less than 500ms between requests
            $suspicious = true;
            $reasons[] = "Rapid requests detected";
        }
    }
    $_SESSION[$sessionKey] = $currentTime;
    
    if ($suspicious) {
        logSecurityEvent('Suspicious Activity', implode(', ', $reasons), 'WARNING');
    }
    
    return $suspicious;
}

/**
 * Validate file upload security
 */
function validateFileUploadSecurity($file) {
    $errors = [];
    
    // Check if file was actually uploaded
    if (!is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'File was not uploaded properly';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    // Check file extension
    $allowedExtensions = ALLOWED_FILE_TYPES;
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($fileExtension, $allowedExtensions)) {
        $errors[] = 'File type not allowed';
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowedMimeTypes = [
        'pdf' => 'application/pdf',
        'txt' => 'text/plain',
        'xml' => ['application/xml', 'text/xml'],
        'csv' => 'text/csv'
    ];
    
    if (isset($allowedMimeTypes[$fileExtension])) {
        $expectedMimes = is_array($allowedMimeTypes[$fileExtension]) 
            ? $allowedMimeTypes[$fileExtension] 
            : [$allowedMimeTypes[$fileExtension]];
            
        if (!in_array($mimeType, $expectedMimes)) {
            $errors[] = 'File content does not match extension';
        }
    }
    
    // Check for malicious content in filename
    if (preg_match('/[<>:"|?*]/', $file['name'])) {
        $errors[] = 'Filename contains invalid characters';
    }
    
    // Check for executable extensions (double extension attack)
    $dangerousExtensions = ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'];
    $fullName = strtolower($file['name']);
    foreach ($dangerousExtensions as $ext) {
        if (strpos($fullName, '.' . $ext) !== false) {
            $errors[] = 'Potentially dangerous file detected';
            break;
        }
    }
    
    return $errors;
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName, $prefix = '') {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    
    // Sanitize basename
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
    $basename = substr($basename, 0, 50); // Limit length
    
    // Add timestamp and random string
    $timestamp = time();
    $random = bin2hex(random_bytes(4));
    
    return $prefix . $basename . '_' . $timestamp . '_' . $random . '.' . $extension;
}

/**
 * Check if IP is whitelisted
 */
function isIPWhitelisted($ip) {
    $whitelist = getSystemSetting('ip_whitelist', '');
    if (empty($whitelist)) {
        return true; // No whitelist configured
    }
    
    $allowedIPs = array_map('trim', explode(',', $whitelist));
    return in_array($ip, $allowedIPs);
}

/**
 * Check if IP is blacklisted
 */
function isIPBlacklisted($ip) {
    $blacklist = getSystemSetting('ip_blacklist', '');
    if (empty($blacklist)) {
        return false; // No blacklist configured
    }
    
    $blockedIPs = array_map('trim', explode(',', $blacklist));
    return in_array($ip, $blockedIPs);
}
