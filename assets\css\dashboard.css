/* Dashboard Styles - Freight & Accounting Portal */

.dashboard {
    padding: 2rem 0;
}

.dashboard h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.module-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.module-card a {
    display: block;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.module-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.module-card:hover .module-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    transform: scale(1.1);
}

.module-card h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 600;
    text-align: center;
}

.module-card p {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.5;
    text-align: center;
    margin: 0;
}

/* Special styling for admin module */
.admin-module {
    border: 2px solid #e74c3c;
    position: relative;
}

.admin-module::before {
    content: 'ADMIN';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

.admin-module:hover {
    border-color: #c0392b;
    box-shadow: 0 8px 30px rgba(231, 76, 60, 0.2);
}

.admin-module:hover .module-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Module-specific colors */
.module-card:nth-child(1):hover .module-icon {
    background: linear-gradient(135deg, #3498db, #2980b9); /* Invoices - Blue */
}

.module-card:nth-child(2):hover .module-icon {
    background: linear-gradient(135deg, #27ae60, #229954); /* Freight - Green */
}

.module-card:nth-child(3):hover .module-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22); /* Tariff - Orange */
}

.module-card:nth-child(4):hover .module-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad); /* Accounting - Purple */
}

.module-card:nth-child(5):hover .module-icon {
    background: linear-gradient(135deg, #34495e, #2c3e50); /* Back Office - Dark */
}

/* Loading animation for modules */
.module-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.module-card.loading .module-icon {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Quick stats section */


/* Recent activity section */
.recent-activity {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
}

.recent-activity h3 {
    padding: 1.5rem;
    margin: 0;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard {
        padding: 1rem 0;
    }
    
    .dashboard h2 {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }
    
    .module-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .module-card a {
        padding: 1.5rem;
    }
    
    .module-icon {
        font-size: 2.5rem;
        height: 70px;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .module-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
