/* Login Page Styles - Freight & Accounting Portal */

.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-box {
    background: white;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-header h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.login-header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

.login-form {
    padding: 2rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.login-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}

.login-form input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.login-form input:focus {
    outline: none;
    border-color: #3498db;
    background: white;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.login-btn:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-footer {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid #e1e8ed;
}

.login-footer p {
    color: #6c757d;
    font-size: 0.85rem;
    margin: 0;
}

/* Alert styles for login page */
.login-form .alert {
    margin-bottom: 1.5rem;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.login-form .alert-error {
    background: #fee;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.login-form .alert-success {
    background: #f0fff4;
    color: #22543d;
    border: 1px solid #c6f6d5;
}

/* Loading state */
.login-btn.loading {
    position: relative;
    color: transparent;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 480px) {
    .login-page {
        padding: 15px;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .login-form {
        padding: 1.5rem;
    }
    
    .login-footer {
        padding: 1rem 1.5rem;
    }
}

/* Accessibility improvements */
.login-form input:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

.login-btn:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Password visibility toggle (if implemented) */
.password-toggle {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.password-toggle-btn:hover {
    color: #495057;
}
