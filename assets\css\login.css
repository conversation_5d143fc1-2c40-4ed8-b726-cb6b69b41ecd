/* Login Page Styles - Freight & Accounting Portal */

.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.login-container {
    width: 100%;
    max-width: 400px;
}

/* Wider container for registration page */
body.register-page .login-container {
    max-width: 500px;
}

.login-box {
    background: white;
    border-radius: 16px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 2rem;
    text-align: center;
}

.logo-container {
    margin-bottom: 1rem;
}

.login-logo {
    max-width: 120px;
    height: auto;
    filter: brightness(0) invert(1); /* Makes logo white */
    opacity: 0.95;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.login-logo:hover {
    transform: scale(1.05);
    opacity: 1;
}

.login-header h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.login-header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

.login-form {
    padding: 2rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.login-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}

.login-form input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    position: relative;
}

.login-form input:hover {
    border-color: #bdc3c7;
    background: #ffffff;
}

.login-form input:focus {
    outline: none;
    border-color: #3498db;
    background: white;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.login-btn:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-links {
    padding: 1rem 2rem;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #e1e8ed;
}

.login-links p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.register-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.register-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.login-footer {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid #e1e8ed;
}

.login-footer p {
    color: #6c757d;
    font-size: 0.85rem;
    margin: 0;
    font-weight: 500;
}

.login-footer p::before {
    content: '🚛';
    margin-right: 8px;
    font-size: 1rem;
}

/* Alert styles for login page */
.login-form .alert {
    margin-bottom: 1.5rem;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.login-form .alert-error {
    background: #fee;
    color: #c53030;
    border: 1px solid #fed7d7;
}

.login-form .alert-success {
    background: #f0fff4;
    color: #22543d;
    border: 1px solid #c6f6d5;
}

/* Loading state */
.login-btn.loading {
    position: relative;
    color: transparent;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 480px) {
    .login-page {
        padding: 15px;
    }

    .login-header {
        padding: 1.5rem;
    }

    .login-header h1 {
        font-size: 1.5rem;
    }

    .login-form {
        padding: 1.5rem;
    }

    .login-footer {
        padding: 1rem 1.5rem;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: 1.5rem;
    }
}

/* Additional accessibility improvements */

.login-btn:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Password visibility toggle (if implemented) */
.password-toggle {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.password-toggle-btn:hover {
    color: #495057;
}
